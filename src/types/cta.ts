// CTA (Call-to-Action) Types for Bot Responses

export type CTAType = 'none' | 'buttons' | 'quick_replies' | 'url' | 'phone';

export type CTAButtonType = 'url' | 'phone' | 'text' | 'postback' | 'quick_reply';

export interface CTAButton {
  id?: string;
  text: string;
  type: CTAButtonType;
  value: string; // URL, phone number, or postback data
  whatsapp_type?: 'url' | 'phone_number' | 'quick_reply'; // WhatsApp-specific button type
  emoji?: string; // Optional emoji for the button
  order?: number; // Display order
}

export interface CTAPayload {
  type: CTAType;
  buttons: CTAButton[];
  max_buttons?: number; // Platform-specific limits (WhatsApp: 3 buttons max)
}

// Predefined CTA templates for common business actions
export interface CTATemplate {
  id: string;
  name: string;
  description: string;
  business_types: string[]; // Which business types this template applies to
  payload: CTAPayload;
}

// Common CTA templates
export const CTA_TEMPLATES: CTATemplate[] = [
  {
    id: 'restaurant_booking',
    name: 'Restaurant Booking',
    description: 'Book a table, view menu, call restaurant',
    business_types: ['restaurant', 'cafe', 'bar'],
    payload: {
      type: 'buttons',
      buttons: [
        {
          text: '📅 Book Table',
          type: 'url',
          value: 'https://your-booking-system.com',
          whatsapp_type: 'url',
          emoji: '📅'
        },
        {
          text: '📋 View Menu',
          type: 'url',
          value: 'https://your-menu.com',
          whatsapp_type: 'url',
          emoji: '📋'
        },
        {
          text: '📞 Call Us',
          type: 'phone',
          value: '+**********',
          whatsapp_type: 'phone_number',
          emoji: '📞'
        }
      ]
    }
  },
  {
    id: 'retail_shopping',
    name: 'Retail Shopping',
    description: 'Shop online, check store hours, get directions',
    business_types: ['retail', 'ecommerce', 'shop'],
    payload: {
      type: 'buttons',
      buttons: [
        {
          text: '🛒 Shop Online',
          type: 'url',
          value: 'https://your-store.com',
          whatsapp_type: 'url',
          emoji: '🛒'
        },
        {
          text: '📍 Get Directions',
          type: 'url',
          value: 'https://maps.google.com/your-location',
          whatsapp_type: 'url',
          emoji: '📍'
        },
        {
          text: '📞 Contact Us',
          type: 'phone',
          value: '+**********',
          whatsapp_type: 'phone_number',
          emoji: '📞'
        }
      ]
    }
  },
  {
    id: 'service_booking',
    name: 'Service Booking',
    description: 'Book appointment, view services, contact business',
    business_types: ['healthcare', 'beauty', 'professional_services'],
    payload: {
      type: 'buttons',
      buttons: [
        {
          text: '📅 Book Appointment',
          type: 'url',
          value: 'https://your-booking.com',
          whatsapp_type: 'url',
          emoji: '📅'
        },
        {
          text: '💼 Our Services',
          type: 'url',
          value: 'https://your-services.com',
          whatsapp_type: 'url',
          emoji: '💼'
        },
        {
          text: '📞 Call Now',
          type: 'phone',
          value: '+**********',
          whatsapp_type: 'phone_number',
          emoji: '📞'
        }
      ]
    }
  },
  {
    id: 'quick_replies',
    name: 'Quick Replies',
    description: 'Common quick response options',
    business_types: ['general'],
    payload: {
      type: 'quick_replies',
      buttons: [
        {
          text: 'Hours',
          type: 'quick_reply',
          value: 'hours',
          emoji: '🕐'
        },
        {
          text: 'Location',
          type: 'quick_reply',
          value: 'location',
          emoji: '📍'
        },
        {
          text: 'Contact',
          type: 'quick_reply',
          value: 'contact',
          emoji: '📞'
        }
      ]
    }
  }
];

// Helper functions
export const getCTATemplatesForBusinessType = (businessType: string): CTATemplate[] => {
  return CTA_TEMPLATES.filter(template => 
    template.business_types.includes(businessType) || 
    template.business_types.includes('general')
  );
};

export const validateCTAPayload = (payload: CTAPayload): { valid: boolean; errors: string[] } => {
  const errors: string[] = [];

  if (!payload.buttons || payload.buttons.length === 0) {
    errors.push('At least one button is required');
  }

  if (payload.buttons && payload.buttons.length > 3) {
    errors.push('Maximum 3 buttons allowed for WhatsApp compatibility');
  }

  payload.buttons?.forEach((button, index) => {
    if (!button.text || button.text.trim().length === 0) {
      errors.push(`Button ${index + 1}: Text is required`);
    }
    if (button.text && button.text.length > 20) {
      errors.push(`Button ${index + 1}: Text must be 20 characters or less`);
    }
    if (!button.value || button.value.trim().length === 0) {
      errors.push(`Button ${index + 1}: Value is required`);
    }
    if (button.type === 'url' && !isValidUrl(button.value)) {
      errors.push(`Button ${index + 1}: Invalid URL format`);
    }
    if (button.type === 'phone' && !isValidPhoneNumber(button.value)) {
      errors.push(`Button ${index + 1}: Invalid phone number format`);
    }
  });

  return {
    valid: errors.length === 0,
    errors
  };
};

const isValidUrl = (url: string): boolean => {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
};

const isValidPhoneNumber = (phone: string): boolean => {
  // Basic phone number validation (can be enhanced)
  const phoneRegex = /^\+?[\d\s\-\(\)]{10,}$/;
  return phoneRegex.test(phone);
};

// WhatsApp-specific formatting
export const formatCTAForWhatsApp = (payload: CTAPayload): any => {
  if (payload.type === 'buttons') {
    return {
      type: 'interactive',
      interactive: {
        type: 'button',
        body: {
          text: 'How can I help you?'
        },
        action: {
          buttons: payload.buttons.slice(0, 3).map((button, index) => ({
            type: 'reply',
            reply: {
              id: `btn_${index}`,
              title: button.text
            }
          }))
        }
      }
    };
  }

  if (payload.type === 'quick_replies') {
    return {
      type: 'interactive',
      interactive: {
        type: 'list',
        body: {
          text: 'What would you like to know?'
        },
        action: {
          button: 'Choose Option',
          sections: [{
            title: 'Options',
            rows: payload.buttons.map((button, index) => ({
              id: `option_${index}`,
              title: button.text,
              description: button.value
            }))
          }]
        }
      }
    };
  }

  return null;
};
