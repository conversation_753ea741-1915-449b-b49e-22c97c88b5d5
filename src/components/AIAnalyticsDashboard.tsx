import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { useAIAnalytics } from '@/hooks/useAIAnalytics';
import { useAuth } from '@/contexts/AuthContext';
import { 
  BarChart3, 
  Brain, 
  Clock, 
  DollarSign, 
  MessageSquare, 
  Star,
  TrendingUp,
  Zap,
  RefreshCw
} from 'lucide-react';

export const AIAnalyticsDashboard: React.FC = () => {
  const { 
    usageStats, 
    recentResponses, 
    loading, 
    updateResponseQuality, 
    getTotalStats,
    getProviderComparison,
    refetch 
  } = useAIAnalytics();
  const { user } = useAuth();

  const totalStats = getTotalStats();
  const providerComparison = getProviderComparison();

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 4,
    }).format(amount);
  };

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat('en-US').format(num);
  };

  const getProviderColor = (provider: string) => {
    switch (provider) {
      case 'OpenAI GPT-3.5':
        return 'bg-green-100 text-green-800';
      case 'Google Gemini Pro':
        return 'bg-blue-100 text-blue-800';
      case 'Mock AI':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-purple-100 text-purple-800';
    }
  };

  if (!user) {
    return (
      <Card>
        <CardContent className="pt-6">
          <p className="text-center text-muted-foreground">
            Please log in to view AI analytics
          </p>
        </CardContent>
      </Card>
    );
  }

  if (loading) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="flex items-center justify-center">
            <RefreshCw className="h-6 w-6 animate-spin mr-2" />
            <span>Loading AI analytics...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold flex items-center gap-2">
            <Brain className="h-6 w-6" />
            AI Analytics Dashboard
          </h2>
          <p className="text-muted-foreground">
            Track your AI usage, costs, and performance across providers
          </p>
        </div>
        <Button onClick={refetch} variant="outline">
          <RefreshCw className="h-4 w-4 mr-2" />
          Refresh
        </Button>
      </div>

      {/* Overview Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Responses</p>
                <p className="text-2xl font-bold">{formatNumber(totalStats.total_responses)}</p>
              </div>
              <MessageSquare className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Tokens</p>
                <p className="text-2xl font-bold">{formatNumber(totalStats.total_tokens)}</p>
              </div>
              <Zap className="h-8 w-8 text-yellow-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Cost</p>
                <p className="text-2xl font-bold">{formatCurrency(totalStats.total_cost)}</p>
              </div>
              <DollarSign className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Avg Quality</p>
                <p className="text-2xl font-bold">
                  {totalStats.avg_quality_score > 0 ? totalStats.avg_quality_score.toFixed(1) : 'N/A'}
                </p>
              </div>
              <Star className="h-8 w-8 text-orange-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Provider Comparison */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Provider Usage</CardTitle>
            <CardDescription>
              Compare usage across different AI providers
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {usageStats.map((stat) => (
                <div key={stat.provider} className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Badge className={getProviderColor(stat.provider)}>
                      {stat.provider}
                    </Badge>
                    <span className="text-sm text-muted-foreground">
                      {formatNumber(stat.total_responses)} responses
                    </span>
                  </div>
                  <Progress 
                    value={(stat.total_responses / totalStats.total_responses) * 100} 
                    className="h-2"
                  />
                  <div className="flex justify-between text-xs text-muted-foreground">
                    <span>{formatCurrency(stat.total_cost)}</span>
                    <span>{stat.avg_processing_time.toFixed(0)}ms avg</span>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Provider Performance</CardTitle>
            <CardDescription>
              Cost efficiency and quality comparison
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {providerComparison.map((provider) => (
                <div key={provider.provider} className="p-3 border rounded-lg">
                  <div className="flex items-center justify-between mb-2">
                    <Badge className={getProviderColor(provider.provider)}>
                      {provider.provider}
                    </Badge>
                    <div className="flex items-center gap-1">
                      <Star className="h-4 w-4 text-yellow-500" />
                      <span className="text-sm">
                        {provider.quality > 0 ? provider.quality.toFixed(1) : 'N/A'}
                      </span>
                    </div>
                  </div>
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <p className="text-muted-foreground">Cost per response</p>
                      <p className="font-medium">{formatCurrency(provider.cost_per_response)}</p>
                    </div>
                    <div>
                      <p className="text-muted-foreground">Efficiency</p>
                      <p className="font-medium">{provider.efficiency.toFixed(2)}</p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Recent Responses */}
      <Card>
        <CardHeader>
          <CardTitle>Recent AI Responses</CardTitle>
          <CardDescription>
            Latest AI-generated responses with quality ratings
          </CardDescription>
        </CardHeader>
        <CardContent>
          {recentResponses.length === 0 ? (
            <div className="text-center py-8">
              <Brain className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
              <p className="text-muted-foreground">No AI responses yet</p>
              <p className="text-sm text-muted-foreground mt-2">
                Start chatting with your bots to see analytics here
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              {recentResponses.slice(0, 10).map((response) => (
                <div key={response.id} className="border rounded-lg p-4">
                  <div className="flex items-start justify-between mb-2">
                    <div className="flex items-center gap-2">
                      <Badge className={getProviderColor(response.provider)}>
                        {response.provider}
                      </Badge>
                      <span className="text-sm text-muted-foreground">
                        {response.message?.bot?.name || 'Unknown Bot'}
                      </span>
                    </div>
                    <div className="flex items-center gap-2">
                      {response.processing_time_ms && (
                        <div className="flex items-center gap-1 text-sm text-muted-foreground">
                          <Clock className="h-3 w-3" />
                          {response.processing_time_ms}ms
                        </div>
                      )}
                      {response.cost_usd && (
                        <div className="flex items-center gap-1 text-sm text-muted-foreground">
                          <DollarSign className="h-3 w-3" />
                          {formatCurrency(response.cost_usd)}
                        </div>
                      )}
                    </div>
                  </div>
                  
                  <p className="text-sm mb-3 line-clamp-2">
                    {response.message?.content || 'No content available'}
                  </p>
                  
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-1">
                      <span className="text-xs text-muted-foreground">Quality:</span>
                      {[1, 2, 3, 4, 5].map((star) => (
                        <button
                          key={star}
                          onClick={() => updateResponseQuality(response.id, star)}
                          className={`h-4 w-4 ${
                            star <= (response.response_quality_score || 0)
                              ? 'text-yellow-500'
                              : 'text-gray-300'
                          }`}
                        >
                          <Star className="h-4 w-4 fill-current" />
                        </button>
                      ))}
                    </div>
                    <span className="text-xs text-muted-foreground">
                      {new Date(response.created_at).toLocaleString()}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};
