import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useBots } from '@/hooks/useBots';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/hooks/use-toast';
import { Loader2 } from 'lucide-react';

export const QuickBotTest: React.FC = () => {
  const [testing, setTesting] = useState(false);
  const { createBot } = useBots();
  const { user } = useAuth();
  const { toast } = useToast();

  const testQuickBot = async () => {
    if (!user) {
      toast({
        title: "Error",
        description: "Please log in first",
        variant: "destructive",
      });
      return;
    }

    setTesting(true);
    try {
      console.log('🧪 Testing quick bot creation...');
      
      const testBotData = {
        name: `Quick Test Bot ${Date.now()}`,
        channel: 'whatsapp' as const,
        description: 'This is a quick test bot',
        tone: 'friendly',
        language: 'English'
      };

      console.log('📤 Sending bot data:', testBotData);
      
      const result = await createBot(testBotData);
      
      if (result) {
        console.log('✅ Bot created successfully:', result);
        toast({
          title: "Success!",
          description: `Test bot "${testBotData.name}" created successfully!`,
        });
      } else {
        console.log('❌ Bot creation returned null');
        toast({
          title: "Failed",
          description: "Bot creation returned null - check console for details",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error('❌ Error in test:', error);
      toast({
        title: "Error",
        description: `Test failed: ${error}`,
        variant: "destructive",
      });
    } finally {
      setTesting(false);
    }
  };

  return (
    <Card className="w-full max-w-md">
      <CardHeader>
        <CardTitle>Quick Bot Test</CardTitle>
        <CardDescription>
          Test bot creation with a simple click
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="text-sm text-muted-foreground">
            <p><strong>User:</strong> {user?.email || 'Not logged in'}</p>
            <p><strong>Status:</strong> {user ? 'Ready to test' : 'Please log in'}</p>
          </div>
          
          <Button 
            onClick={testQuickBot} 
            disabled={testing || !user}
            className="w-full"
          >
            {testing ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Creating Test Bot...
              </>
            ) : (
              'Create Test Bot'
            )}
          </Button>
          
          <div className="text-xs text-muted-foreground">
            <p>This will create a test bot with predefined settings. Check the console for detailed logs.</p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
