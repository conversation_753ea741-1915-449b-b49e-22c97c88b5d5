import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { aiService } from '@/services/aiService';
import { useToast } from '@/hooks/use-toast';
import { Loader2, MessageSquare, Zap } from 'lucide-react';

export const AIChatTester: React.FC = () => {
  const [testMessage, setTestMessage] = useState('Hello, how can you help me?');
  const [response, setResponse] = useState<string>('');
  const [loading, setLoading] = useState(false);
  const [provider, setProvider] = useState<string>('');
  const [processingTime, setProcessingTime] = useState<number>(0);
  const { toast } = useToast();

  const testAIResponse = async () => {
    if (!testMessage.trim()) {
      toast({
        title: "Error",
        description: "Please enter a test message",
        variant: "destructive",
      });
      return;
    }

    setLoading(true);
    setResponse('');
    setProvider('');
    setProcessingTime(0);

    try {
      console.log('🧪 Testing AI service...');
      
      const aiResponse = await aiService.generateResponse(testMessage, {
        botName: 'Test Bot',
        tone: 'friendly',
        language: 'English',
        systemPrompt: 'You are a helpful customer service assistant.'
      });

      setResponse(aiResponse.content);
      setProvider(aiResponse.provider);
      setProcessingTime(aiResponse.processingTime || 0);

      toast({
        title: "Success!",
        description: `AI response generated in ${aiResponse.processingTime}ms`,
      });

    } catch (error: any) {
      console.error('❌ AI test failed:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to generate AI response",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const availableProviders = aiService.getAvailableProviders();

  return (
    <Card className="w-full max-w-2xl">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Zap className="h-5 w-5" />
          AI Service Tester
        </CardTitle>
        <CardDescription>
          Test the AI service integration and response generation
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Provider Info */}
        <div className="flex items-center gap-2">
          <span className="text-sm font-medium">Available Providers:</span>
          {availableProviders.map((providerName) => (
            <Badge key={providerName} variant="outline">
              {providerName}
            </Badge>
          ))}
        </div>

        {/* Test Input */}
        <div className="space-y-2">
          <label className="text-sm font-medium">Test Message:</label>
          <Input
            value={testMessage}
            onChange={(e) => setTestMessage(e.target.value)}
            placeholder="Enter a message to test..."
            disabled={loading}
          />
        </div>

        {/* Test Button */}
        <Button 
          onClick={testAIResponse} 
          disabled={loading || !testMessage.trim()}
          className="w-full"
        >
          {loading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Generating AI Response...
            </>
          ) : (
            <>
              <MessageSquare className="mr-2 h-4 w-4" />
              Test AI Response
            </>
          )}
        </Button>

        {/* Response Display */}
        {response && (
          <div className="space-y-3">
            <div className="border-t pt-4">
              <h4 className="font-medium mb-2">AI Response:</h4>
              <div className="bg-gray-50 p-3 rounded-lg">
                <p className="text-sm whitespace-pre-wrap">{response}</p>
              </div>
            </div>

            <div className="flex items-center gap-4 text-sm text-muted-foreground">
              <span>Provider: <strong>{provider}</strong></span>
              <span>Time: <strong>{processingTime}ms</strong></span>
            </div>
          </div>
        )}

        {/* Instructions */}
        <div className="text-xs text-muted-foreground border-t pt-4">
          <p className="mb-2"><strong>How it works:</strong></p>
          <ul className="space-y-1">
            <li>• If you have VITE_OPENAI_API_KEY set, it will use OpenAI GPT-3.5</li>
            <li>• Otherwise, it will use a mock AI for demonstration</li>
            <li>• The response includes context awareness and bot personality</li>
            <li>• Processing time and provider information are tracked</li>
          </ul>
        </div>
      </CardContent>
    </Card>
  );
};
