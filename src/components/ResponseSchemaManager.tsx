import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useResponseSchemas } from '@/hooks/useResponseSchemas';
import { useBots } from '@/hooks/useBots';
import { useAuth } from '@/contexts/AuthContext';
import { aiService } from '@/services/aiService';
import { 
  Bot, 
  MessageSquare, 
  Zap, 
  CheckCircle, 
  XCircle, 
  Edit3, 
  Trash2,
  Loader2,
  Brain,
  Target
} from 'lucide-react';

interface ResponseSchemaManagerProps {
  botId?: string;
}

export const ResponseSchemaManager: React.FC<ResponseSchemaManagerProps> = ({ botId }) => {
  const [selectedBotId, setSelectedBotId] = useState(botId || '');
  const [selectedCategory, setSelectedCategory] = useState('restaurant');
  const [selectedProvider, setSelectedProvider] = useState('openai');
  const [editingSchema, setEditingSchema] = useState<string | null>(null);
  const [editText, setEditText] = useState('');

  const { getActiveBots } = useBots();
  const { user } = useAuth();
  const {
    businessCategories,
    responseSchemas,
    botCoverage,
    loading,
    generating,
    generateResponseSchemas,
    updateResponseSchema,
    deleteResponseSchema,
  } = useResponseSchemas(selectedBotId);

  const activeBots = getActiveBots();
  const availableProviders = aiService.getAvailableProviders();

  const handleGenerateSchemas = async () => {
    if (!selectedBotId) return;
    await generateResponseSchemas(selectedBotId, selectedCategory, selectedProvider);
  };

  const handleEditSchema = (schemaId: string, currentResponse: string) => {
    setEditingSchema(schemaId);
    setEditText(currentResponse);
  };

  const handleSaveEdit = async (schemaId: string) => {
    await updateResponseSchema(schemaId, {
      custom_response: editText,
      is_approved: true,
    });
    setEditingSchema(null);
    setEditText('');
  };

  const handleApproveSchema = async (schemaId: string) => {
    await updateResponseSchema(schemaId, { is_approved: true });
  };

  if (!user) {
    return (
      <Card>
        <CardContent className="pt-6">
          <p className="text-center text-muted-foreground">
            Please log in to manage response schemas
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h2 className="text-2xl font-bold flex items-center gap-2">
          <Target className="h-6 w-6" />
          Response Schema Manager
        </h2>
        <p className="text-muted-foreground">
          Generate and manage AI responses for common customer questions
        </p>
      </div>

      {/* Configuration */}
      <Card>
        <CardHeader>
          <CardTitle>Configuration</CardTitle>
          <CardDescription>
            Select your bot and business type to generate response schemas
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="text-sm font-medium">Select Bot:</label>
              <Select value={selectedBotId} onValueChange={setSelectedBotId}>
                <SelectTrigger>
                  <SelectValue placeholder="Choose a bot" />
                </SelectTrigger>
                <SelectContent>
                  {activeBots.map((bot) => (
                    <SelectItem key={bot.id} value={bot.id}>
                      <div className="flex items-center gap-2">
                        <Bot className="h-4 w-4" />
                        {bot.name}
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <label className="text-sm font-medium">Business Type:</label>
              <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {businessCategories.map((category) => (
                    <SelectItem key={category.id} value={category.name}>
                      {category.name.replace('_', ' ').toUpperCase()}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <label className="text-sm font-medium">AI Provider:</label>
              <Select value={selectedProvider} onValueChange={setSelectedProvider}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {availableProviders.map((provider) => (
                    <SelectItem key={provider} value={provider}>
                      <div className="flex items-center gap-2">
                        <Brain className="h-4 w-4" />
                        {provider === 'openai' ? 'OpenAI GPT-3.5' :
                         provider === 'gemini' ? 'Google Gemini Pro' :
                         provider === 'mock' ? 'Mock AI' : provider}
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <Button 
            onClick={handleGenerateSchemas}
            disabled={!selectedBotId || generating}
            className="w-full"
          >
            {generating ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Generating Response Schemas...
              </>
            ) : (
              <>
                <Zap className="mr-2 h-4 w-4" />
                Generate Response Schemas
              </>
            )}
          </Button>
        </CardContent>
      </Card>

      {/* Coverage Stats */}
      {botCoverage && (
        <Card>
          <CardHeader>
            <CardTitle>Coverage Statistics</CardTitle>
            <CardDescription>
              How many expected questions have AI-generated responses
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Response Coverage</span>
                <span className="text-sm text-muted-foreground">
                  {botCoverage.answered_questions} / {botCoverage.total_questions} questions
                </span>
              </div>
              <Progress value={botCoverage.coverage_percentage} className="h-2" />
              <p className="text-sm text-muted-foreground">
                {botCoverage.coverage_percentage}% of expected {botCoverage.business_category} questions have responses
              </p>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Response Schemas */}
      <Card>
        <CardHeader>
          <CardTitle>Generated Response Schemas</CardTitle>
          <CardDescription>
            Review and customize AI-generated responses for your bot
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="h-6 w-6 animate-spin mr-2" />
              <span>Loading response schemas...</span>
            </div>
          ) : responseSchemas.length === 0 ? (
            <div className="text-center py-8">
              <MessageSquare className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
              <p className="text-muted-foreground">No response schemas generated yet</p>
              <p className="text-sm text-muted-foreground mt-2">
                Select a bot and click "Generate Response Schemas" to get started
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              {responseSchemas.map((schema) => (
                <div key={schema.id} className="border rounded-lg p-4">
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex-1">
                      <h4 className="font-medium text-sm">
                        {schema.custom_question || schema.question_template?.question_text}
                      </h4>
                      <div className="flex items-center gap-2 mt-1">
                        <Badge variant="outline" className="text-xs">
                          {schema.custom_intent || schema.question_template?.question_intent}
                        </Badge>
                        <Badge variant="outline" className="text-xs">
                          {schema.provider_used}
                        </Badge>
                        {schema.custom_question && (
                          <Badge variant="secondary" className="text-xs">
                            Custom
                          </Badge>
                        )}
                        {schema.is_approved ? (
                          <Badge className="text-xs bg-green-100 text-green-800">
                            <CheckCircle className="h-3 w-3 mr-1" />
                            Approved
                          </Badge>
                        ) : (
                          <Badge variant="secondary" className="text-xs">
                            <XCircle className="h-3 w-3 mr-1" />
                            Pending Review
                          </Badge>
                        )}
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleEditSchema(schema.id, schema.custom_response || schema.ai_generated_response)}
                      >
                        <Edit3 className="h-4 w-4" />
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => deleteResponseSchema(schema.id)}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>

                  {editingSchema === schema.id ? (
                    <div className="space-y-3">
                      <Textarea
                        value={editText}
                        onChange={(e) => setEditText(e.target.value)}
                        rows={4}
                        placeholder="Edit the response..."
                      />
                      <div className="flex gap-2">
                        <Button size="sm" onClick={() => handleSaveEdit(schema.id)}>
                          Save Changes
                        </Button>
                        <Button 
                          size="sm" 
                          variant="outline" 
                          onClick={() => setEditingSchema(null)}
                        >
                          Cancel
                        </Button>
                      </div>
                    </div>
                  ) : (
                    <div className="space-y-3">
                      <div className="bg-gray-50 p-3 rounded text-sm">
                        {schema.custom_response || schema.ai_generated_response}
                      </div>
                      {!schema.is_approved && (
                        <Button 
                          size="sm" 
                          onClick={() => handleApproveSchema(schema.id)}
                        >
                          <CheckCircle className="h-4 w-4 mr-2" />
                          Approve Response
                        </Button>
                      )}
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};
