import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { 
  Building2, 
  Clock, 
  MapPin, 
  Phone, 
  Mail, 
  Globe,
  Save,
  Loader2
} from 'lucide-react';

export interface BusinessInfo {
  business_name: string;
  business_type: string;
  description: string;
  address: string;
  phone: string;
  email: string;
  website: string;
  hours: {
    monday: string;
    tuesday: string;
    wednesday: string;
    thursday: string;
    friday: string;
    saturday: string;
    sunday: string;
  };
  services: string[];
  special_info: string;
}

interface BusinessInfoFormProps {
  botId: string;
  initialData?: Partial<BusinessInfo>;
  onSave: (businessInfo: BusinessInfo) => Promise<void>;
  loading?: boolean;
}

export const BusinessInfoForm: React.FC<BusinessInfoFormProps> = ({
  botId,
  initialData,
  onSave,
  loading = false
}) => {
  const [businessInfo, setBusinessInfo] = useState<BusinessInfo>({
    business_name: initialData?.business_name || '',
    business_type: initialData?.business_type || 'restaurant',
    description: initialData?.description || '',
    address: initialData?.address || '',
    phone: initialData?.phone || '',
    email: initialData?.email || '',
    website: initialData?.website || '',
    hours: initialData?.hours || {
      monday: '9:00 AM - 9:00 PM',
      tuesday: '9:00 AM - 9:00 PM',
      wednesday: '9:00 AM - 9:00 PM',
      thursday: '9:00 AM - 9:00 PM',
      friday: '9:00 AM - 10:00 PM',
      saturday: '9:00 AM - 10:00 PM',
      sunday: '10:00 AM - 8:00 PM',
    },
    services: initialData?.services || [],
    special_info: initialData?.special_info || '',
  });

  const [newService, setNewService] = useState('');
  const [saving, setSaving] = useState(false);
  const { toast } = useToast();

  const businessTypes = [
    { value: 'restaurant', label: 'Restaurant / Food Service' },
    { value: 'retail', label: 'Retail Store' },
    { value: 'healthcare', label: 'Healthcare / Medical' },
    { value: 'real_estate', label: 'Real Estate' },
    { value: 'automotive', label: 'Automotive' },
    { value: 'beauty_wellness', label: 'Beauty & Wellness' },
    { value: 'education', label: 'Education / Training' },
    { value: 'professional_services', label: 'Professional Services' },
    { value: 'hospitality', label: 'Hospitality / Travel' },
    { value: 'general', label: 'General Business' },
  ];

  const handleSave = async () => {
    if (!businessInfo.business_name.trim()) {
      toast({
        title: "Validation Error",
        description: "Business name is required",
        variant: "destructive",
      });
      return;
    }

    setSaving(true);
    try {
      await onSave(businessInfo);
      toast({
        title: "Success",
        description: "Business information saved successfully",
      });
    } catch (error) {
      console.error('Error saving business info:', error);
      toast({
        title: "Error",
        description: "Failed to save business information",
        variant: "destructive",
      });
    } finally {
      setSaving(false);
    }
  };

  const addService = () => {
    if (newService.trim() && !businessInfo.services.includes(newService.trim())) {
      setBusinessInfo(prev => ({
        ...prev,
        services: [...prev.services, newService.trim()]
      }));
      setNewService('');
    }
  };

  const removeService = (service: string) => {
    setBusinessInfo(prev => ({
      ...prev,
      services: prev.services.filter(s => s !== service)
    }));
  };

  const updateHours = (day: keyof BusinessInfo['hours'], hours: string) => {
    setBusinessInfo(prev => ({
      ...prev,
      hours: {
        ...prev.hours,
        [day]: hours
      }
    }));
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Building2 className="h-5 w-5" />
            Business Information
          </CardTitle>
          <CardDescription>
            Provide details about your business to help AI generate better responses
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Basic Info */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="business_name">Business Name *</Label>
              <Input
                id="business_name"
                value={businessInfo.business_name}
                onChange={(e) => setBusinessInfo(prev => ({ ...prev, business_name: e.target.value }))}
                placeholder="e.g., Mario's Italian Restaurant"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="business_type">Business Type *</Label>
              <Select
                value={businessInfo.business_type}
                onValueChange={(value) => setBusinessInfo(prev => ({ ...prev, business_type: value }))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {businessTypes.map((type) => (
                    <SelectItem key={type.value} value={type.value}>
                      {type.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">Business Description</Label>
            <Textarea
              id="description"
              value={businessInfo.description}
              onChange={(e) => setBusinessInfo(prev => ({ ...prev, description: e.target.value }))}
              placeholder="Describe your business, what makes it special, target customers, etc."
              rows={3}
            />
          </div>

          {/* Contact Info */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="phone" className="flex items-center gap-2">
                <Phone className="h-4 w-4" />
                Phone Number
              </Label>
              <Input
                id="phone"
                value={businessInfo.phone}
                onChange={(e) => setBusinessInfo(prev => ({ ...prev, phone: e.target.value }))}
                placeholder="+****************"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="email" className="flex items-center gap-2">
                <Mail className="h-4 w-4" />
                Email
              </Label>
              <Input
                id="email"
                type="email"
                value={businessInfo.email}
                onChange={(e) => setBusinessInfo(prev => ({ ...prev, email: e.target.value }))}
                placeholder="<EMAIL>"
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="address" className="flex items-center gap-2">
                <MapPin className="h-4 w-4" />
                Address
              </Label>
              <Input
                id="address"
                value={businessInfo.address}
                onChange={(e) => setBusinessInfo(prev => ({ ...prev, address: e.target.value }))}
                placeholder="123 Main St, City, State 12345"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="website" className="flex items-center gap-2">
                <Globe className="h-4 w-4" />
                Website
              </Label>
              <Input
                id="website"
                value={businessInfo.website}
                onChange={(e) => setBusinessInfo(prev => ({ ...prev, website: e.target.value }))}
                placeholder="https://www.business.com"
              />
            </div>
          </div>

          {/* Services */}
          <div className="space-y-3">
            <Label>Services / Products</Label>
            <div className="flex gap-2">
              <Input
                value={newService}
                onChange={(e) => setNewService(e.target.value)}
                placeholder="Add a service or product"
                onKeyPress={(e) => e.key === 'Enter' && addService()}
              />
              <Button onClick={addService} variant="outline">
                Add
              </Button>
            </div>
            <div className="flex flex-wrap gap-2">
              {businessInfo.services.map((service, index) => (
                <Badge key={index} variant="secondary" className="cursor-pointer">
                  {service}
                  <button
                    onClick={() => removeService(service)}
                    className="ml-2 hover:text-red-500"
                  >
                    ×
                  </button>
                </Badge>
              ))}
            </div>
          </div>

          {/* Special Information */}
          <div className="space-y-2">
            <Label htmlFor="special_info">Special Information</Label>
            <Textarea
              id="special_info"
              value={businessInfo.special_info}
              onChange={(e) => setBusinessInfo(prev => ({ ...prev, special_info: e.target.value }))}
              placeholder="Any special policies, unique features, seasonal information, etc."
              rows={3}
            />
          </div>
        </CardContent>
      </Card>

      {/* Business Hours */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="h-5 w-5" />
            Business Hours
          </CardTitle>
          <CardDescription>
            Set your operating hours for each day of the week
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {Object.entries(businessInfo.hours).map(([day, hours]) => (
              <div key={day} className="flex items-center gap-4">
                <Label className="w-24 capitalize">{day}:</Label>
                <Input
                  value={hours}
                  onChange={(e) => updateHours(day as keyof BusinessInfo['hours'], e.target.value)}
                  placeholder="9:00 AM - 5:00 PM or 'Closed'"
                  className="flex-1"
                />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Save Button */}
      <Button 
        onClick={handleSave} 
        disabled={saving || loading}
        className="w-full"
        size="lg"
      >
        {saving ? (
          <>
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            Saving Business Information...
          </>
        ) : (
          <>
            <Save className="mr-2 h-4 w-4" />
            Save Business Information
          </>
        )}
      </Button>
    </div>
  );
};
