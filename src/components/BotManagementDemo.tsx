import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { CreateBotDialog } from './CreateBotDialog';
import { BotCreationTester } from './BotCreationTester';
import { useBots } from '@/hooks/useBots';
import { useAuth } from '@/contexts/AuthContext';
import { MessageSquare, Phone, Facebook, Bot, Trash2, Pause, Play, Archive } from 'lucide-react';

export const BotManagementDemo: React.FC = () => {
  const { bots, loading, deleteBot, updateBotStatus, getActiveBots, getBotsByChannel } = useBots();
  const { user } = useAuth();

  const getChannelIcon = (channel: string) => {
    switch (channel) {
      case 'whatsapp':
        return <MessageSquare className="h-4 w-4" />;
      case 'sms':
        return <Phone className="h-4 w-4" />;
      case 'messenger':
        return <Facebook className="h-4 w-4" />;
      default:
        return <Bot className="h-4 w-4" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'paused':
        return 'bg-yellow-100 text-yellow-800';
      case 'archived':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const handleBotCreated = (bot: any) => {
    console.log('New bot created:', bot);
  };

  if (!user) {
    return (
      <Card>
        <CardContent className="pt-6">
          <p className="text-center text-muted-foreground">
            Please log in to manage your bots
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold">Bot Management</h2>
          <p className="text-muted-foreground">
            Create and manage your AI bots across different channels
          </p>
        </div>
        <CreateBotDialog onBotCreated={handleBotCreated} />
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="pt-6">
            <div className="text-2xl font-bold">{bots.length}</div>
            <p className="text-xs text-muted-foreground">Total Bots</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="pt-6">
            <div className="text-2xl font-bold">{getActiveBots().length}</div>
            <p className="text-xs text-muted-foreground">Active Bots</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="pt-6">
            <div className="text-2xl font-bold">{getBotsByChannel('whatsapp').length}</div>
            <p className="text-xs text-muted-foreground">WhatsApp Bots</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="pt-6">
            <div className="text-2xl font-bold">{getBotsByChannel('sms').length + getBotsByChannel('messenger').length}</div>
            <p className="text-xs text-muted-foreground">SMS & Messenger</p>
          </CardContent>
        </Card>
      </div>

      {/* Bot List */}
      <Card>
        <CardHeader>
          <CardTitle>Your Bots</CardTitle>
          <CardDescription>
            Manage your AI bots and their configurations
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="text-center py-8">
              <p className="text-muted-foreground">Loading bots...</p>
            </div>
          ) : bots.length === 0 ? (
            <div className="text-center py-8">
              <Bot className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
              <p className="text-muted-foreground mb-4">No bots created yet</p>
              <CreateBotDialog 
                trigger={<Button>Create Your First Bot</Button>}
                onBotCreated={handleBotCreated}
              />
            </div>
          ) : (
            <div className="space-y-4">
              {bots.map((bot) => (
                <div key={bot.id} className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex items-center space-x-4">
                    <div className="flex items-center space-x-2">
                      {getChannelIcon(bot.channel)}
                      <div>
                        <h3 className="font-medium">{bot.name}</h3>
                        <p className="text-sm text-muted-foreground">
                          {bot.channel} • Created {new Date(bot.created_at).toLocaleDateString()}
                        </p>
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <Badge className={getStatusColor(bot.status)}>
                      {bot.status}
                    </Badge>
                    
                    <div className="flex space-x-1">
                      {bot.status === 'active' ? (
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => updateBotStatus(bot.id, 'paused')}
                        >
                          <Pause className="h-4 w-4" />
                        </Button>
                      ) : bot.status === 'paused' ? (
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => updateBotStatus(bot.id, 'active')}
                        >
                          <Play className="h-4 w-4" />
                        </Button>
                      ) : null}
                      
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => updateBotStatus(bot.id, 'archived')}
                      >
                        <Archive className="h-4 w-4" />
                      </Button>
                      
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => deleteBot(bot.id)}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Testing Component */}
      <BotCreationTester />
    </div>
  );
};
