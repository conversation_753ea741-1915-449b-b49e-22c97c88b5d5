import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useBots } from '@/hooks/useBots';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/hooks/use-toast';
import { Loader2, Bot, CheckCircle, XCircle } from 'lucide-react';

export const BotCreationTester: React.FC = () => {
  const [testing, setTesting] = useState(false);
  const [testResults, setTestResults] = useState<any[]>([]);
  const { createBot, bots, loading } = useBots();
  const { user } = useAuth();
  const { toast } = useToast();

  const testBotCreation = async () => {
    if (!user) {
      toast({
        title: "Error",
        description: "Please log in first",
        variant: "destructive",
      });
      return;
    }

    setTesting(true);
    setTestResults([]);

    const testBots = [
      {
        name: `Test WhatsApp Bot ${Date.now()}`,
        channel: 'whatsapp' as const,
        description: 'Test bot for WhatsApp integration'
      },
      {
        name: `Test SMS Bot ${Date.now()}`,
        channel: 'sms' as const,
        description: 'Test bot for SMS integration'
      },
      {
        name: `Test Messenger Bot ${Date.now()}`,
        channel: 'messenger' as const,
        description: 'Test bot for Facebook Messenger integration'
      }
    ];

    const results = [];

    for (const botData of testBots) {
      try {
        console.log(`Testing creation of ${botData.name}...`);
        const result = await createBot(botData);
        
        results.push({
          ...botData,
          success: !!result,
          result: result,
          error: null
        });

        // Small delay between creations
        await new Promise(resolve => setTimeout(resolve, 500));
      } catch (error) {
        console.error(`Failed to create ${botData.name}:`, error);
        results.push({
          ...botData,
          success: false,
          result: null,
          error: error
        });
      }
    }

    setTestResults(results);
    setTesting(false);

    const successCount = results.filter(r => r.success).length;
    toast({
      title: "Test Complete",
      description: `${successCount}/${results.length} bots created successfully`,
      variant: successCount === results.length ? "default" : "destructive",
    });
  };

  return (
    <Card className="w-full max-w-2xl">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Bot className="h-5 w-5" />
          Bot Creation Tester
        </CardTitle>
        <CardDescription>
          Test bot creation functionality with Supabase integration
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm text-muted-foreground">
              Current user: {user?.email || 'Not logged in'}
            </p>
            <p className="text-sm text-muted-foreground">
              Existing bots: {bots.length}
            </p>
          </div>
          <Button 
            onClick={testBotCreation} 
            disabled={testing || loading || !user}
          >
            {testing ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Testing...
              </>
            ) : (
              'Test Bot Creation'
            )}
          </Button>
        </div>

        {testResults.length > 0 && (
          <div className="space-y-2">
            <h4 className="font-medium">Test Results:</h4>
            {testResults.map((result, index) => (
              <div 
                key={index}
                className="flex items-center justify-between p-3 border rounded-lg"
              >
                <div>
                  <p className="font-medium">{result.name}</p>
                  <p className="text-sm text-muted-foreground">
                    Channel: {result.channel}
                  </p>
                </div>
                <div className="flex items-center gap-2">
                  {result.success ? (
                    <CheckCircle className="h-5 w-5 text-green-500" />
                  ) : (
                    <XCircle className="h-5 w-5 text-red-500" />
                  )}
                  <span className="text-sm">
                    {result.success ? 'Success' : 'Failed'}
                  </span>
                </div>
              </div>
            ))}
          </div>
        )}

        <div className="text-xs text-muted-foreground">
          <p>
            <strong>Note:</strong> This will create actual test bots in your database. 
            You can delete them afterwards if needed.
          </p>
        </div>
      </CardContent>
    </Card>
  );
};
