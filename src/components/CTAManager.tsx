import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Plus, Trash2, ExternalLink, Phone, MessageSquare } from 'lucide-react';
import { CTAButton, CTAPayload, CTA_TEMPLATES, getCTATemplatesForBusinessType } from '@/types/cta';

interface CTAManagerProps {
  businessType?: string;
  onCTAChange: (cta: CTAPayload | null) => void;
  initialCTA?: CTAPayload | null;
}

export const CTAManager: React.FC<CTAManagerProps> = ({
  businessType = 'general',
  onCTAChange,
  initialCTA
}) => {
  const [ctaPayload, setCTAPayload] = useState<CTAPayload>(
    initialCTA || { type: 'none', buttons: [] }
  );

  const availableTemplates = getCTATemplatesForBusinessType(businessType);

  const updateCTA = (newPayload: CTAPayload) => {
    setCTAPayload(newPayload);
    onCTAChange(newPayload.type === 'none' ? null : newPayload);
  };

  const addButton = () => {
    if (ctaPayload.buttons.length >= 3) return; // WhatsApp limit

    const newButton: CTAButton = {
      text: '',
      type: 'url',
      value: '',
      whatsapp_type: 'url'
    };

    updateCTA({
      ...ctaPayload,
      type: 'buttons',
      buttons: [...ctaPayload.buttons, newButton]
    });
  };

  const updateButton = (index: number, field: keyof CTAButton, value: string) => {
    const updatedButtons = [...ctaPayload.buttons];
    updatedButtons[index] = { ...updatedButtons[index], [field]: value };

    // Auto-set whatsapp_type based on type
    if (field === 'type') {
      updatedButtons[index].whatsapp_type = value === 'phone' ? 'phone_number' : 
                                           value === 'url' ? 'url' : 'quick_reply';
    }

    updateCTA({
      ...ctaPayload,
      buttons: updatedButtons
    });
  };

  const removeButton = (index: number) => {
    const updatedButtons = ctaPayload.buttons.filter((_, i) => i !== index);
    updateCTA({
      ...ctaPayload,
      buttons: updatedButtons,
      type: updatedButtons.length === 0 ? 'none' : ctaPayload.type
    });
  };

  const applyTemplate = (templateId: string) => {
    const template = CTA_TEMPLATES.find(t => t.id === templateId);
    if (template) {
      updateCTA(template.payload);
    }
  };

  const getButtonIcon = (type: string) => {
    switch (type) {
      case 'url': return <ExternalLink className="w-4 h-4" />;
      case 'phone': return <Phone className="w-4 h-4" />;
      case 'quick_reply': return <MessageSquare className="w-4 h-4" />;
      default: return null;
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <MessageSquare className="w-5 h-5" />
          Call-to-Action Buttons
        </CardTitle>
        <p className="text-sm text-muted-foreground">
          Add interactive buttons to help customers take action (max 3 for WhatsApp)
        </p>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Templates */}
        {availableTemplates.length > 0 && (
          <div>
            <Label className="text-sm font-medium">Quick Templates</Label>
            <div className="flex flex-wrap gap-2 mt-2">
              {availableTemplates.map((template) => (
                <Button
                  key={template.id}
                  variant="outline"
                  size="sm"
                  onClick={() => applyTemplate(template.id)}
                  className="text-xs"
                >
                  {template.name}
                </Button>
              ))}
            </div>
          </div>
        )}

        {/* CTA Type */}
        <div>
          <Label htmlFor="cta-type">CTA Type</Label>
          <Select
            value={ctaPayload.type}
            onValueChange={(value) => updateCTA({ ...ctaPayload, type: value as any })}
          >
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="none">No CTA</SelectItem>
              <SelectItem value="buttons">Action Buttons</SelectItem>
              <SelectItem value="quick_replies">Quick Replies</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Buttons */}
        {ctaPayload.type !== 'none' && (
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <Label>Buttons ({ctaPayload.buttons.length}/3)</Label>
              <Button
                variant="outline"
                size="sm"
                onClick={addButton}
                disabled={ctaPayload.buttons.length >= 3}
              >
                <Plus className="w-4 h-4 mr-1" />
                Add Button
              </Button>
            </div>

            {ctaPayload.buttons.map((button, index) => (
              <Card key={index} className="p-3">
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <Badge variant="outline" className="text-xs">
                      Button {index + 1}
                    </Badge>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => removeButton(index)}
                      className="text-red-500 hover:text-red-700"
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </div>

                  <div className="grid grid-cols-2 gap-3">
                    <div>
                      <Label className="text-xs">Button Text (max 20 chars)</Label>
                      <Input
                        value={button.text}
                        onChange={(e) => updateButton(index, 'text', e.target.value.slice(0, 20))}
                        placeholder="e.g., Book Now"
                        className="text-sm"
                      />
                    </div>
                    <div>
                      <Label className="text-xs">Type</Label>
                      <Select
                        value={button.type}
                        onValueChange={(value) => updateButton(index, 'type', value)}
                      >
                        <SelectTrigger className="text-sm">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="url">
                            <div className="flex items-center gap-2">
                              <ExternalLink className="w-3 h-3" />
                              Website URL
                            </div>
                          </SelectItem>
                          <SelectItem value="phone">
                            <div className="flex items-center gap-2">
                              <Phone className="w-3 h-3" />
                              Phone Number
                            </div>
                          </SelectItem>
                          <SelectItem value="quick_reply">
                            <div className="flex items-center gap-2">
                              <MessageSquare className="w-3 h-3" />
                              Quick Reply
                            </div>
                          </SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div>
                    <Label className="text-xs">
                      {button.type === 'url' ? 'URL' : 
                       button.type === 'phone' ? 'Phone Number' : 'Reply Text'}
                    </Label>
                    <Input
                      value={button.value}
                      onChange={(e) => updateButton(index, 'value', e.target.value)}
                      placeholder={
                        button.type === 'url' ? 'https://example.com' :
                        button.type === 'phone' ? '+1234567890' :
                        'Quick reply text'
                      }
                      className="text-sm"
                    />
                  </div>

                  {/* Preview */}
                  <div className="pt-2 border-t">
                    <Label className="text-xs text-muted-foreground">Preview:</Label>
                    <Button
                      variant="outline"
                      size="sm"
                      className="mt-1 text-xs"
                      disabled
                    >
                      {getButtonIcon(button.type)}
                      <span className="ml-1">{button.text || 'Button Text'}</span>
                    </Button>
                  </div>
                </div>
              </Card>
            ))}
          </div>
        )}

        {/* Preview Section */}
        {ctaPayload.type !== 'none' && ctaPayload.buttons.length > 0 && (
          <div className="pt-4 border-t">
            <Label className="text-sm font-medium">WhatsApp Preview</Label>
            <div className="mt-2 p-3 bg-gray-50 rounded-lg">
              <div className="bg-white p-3 rounded-lg shadow-sm max-w-sm">
                <p className="text-sm text-gray-800 mb-3">
                  Your response message will appear here...
                </p>
                <div className="space-y-2">
                  {ctaPayload.buttons.map((button, index) => (
                    <div
                      key={index}
                      className="flex items-center gap-2 p-2 border rounded text-sm text-blue-600"
                    >
                      {getButtonIcon(button.type)}
                      {button.text || `Button ${index + 1}`}
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
