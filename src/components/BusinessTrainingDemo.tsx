import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { aiService } from '@/services/aiService';
import { useToast } from '@/hooks/use-toast';
import { 
  Brain, 
  Zap, 
  Loader2,
  MessageSquare,
  CheckCircle
} from 'lucide-react';

export const BusinessTrainingDemo: React.FC = () => {
  const [businessInfo, setBusinessInfo] = useState({
    business_name: "Mario's Italian Restaurant",
    business_type: "restaurant",
    description: "Authentic Italian cuisine with fresh pasta made daily. Family-owned restaurant serving traditional recipes passed down through generations.",
    address: "123 Main Street, Downtown, NY 10001",
    phone: "(*************",
    email: "<EMAIL>",
    website: "www.mariositalian.com",
    hours: {
      monday: "11:00 AM - 10:00 PM",
      tuesday: "11:00 AM - 10:00 PM", 
      wednesday: "11:00 AM - 10:00 PM",
      thursday: "11:00 AM - 10:00 PM",
      friday: "11:00 AM - 11:00 PM",
      saturday: "12:00 PM - 11:00 PM",
      sunday: "12:00 PM - 9:00 PM"
    },
    services: ["Dine-in", "Takeout", "Delivery", "Catering", "Private Events"],
    special_info: "We offer gluten-free pasta options. Reservations recommended for parties of 6 or more. Happy hour Monday-Friday 3-6 PM with 20% off appetizers."
  });

  const [documents] = useState([
    {
      file_name: "menu.pdf",
      content: "APPETIZERS: Bruschetta $8, Calamari $12, Antipasto Platter $15. PASTA: Spaghetti Carbonara $16, Fettuccine Alfredo $14, Lasagna $18. PIZZA: Margherita $14, Pepperoni $16, Quattro Stagioni $18. DESSERTS: Tiramisu $8, Cannoli $6, Gelato $5."
    }
  ]);

  const [generatedQAs, setGeneratedQAs] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedProvider, setSelectedProvider] = useState('openai');
  const { toast } = useToast();

  const availableProviders = aiService.getAvailableProviders();

  const handleGenerateQAs = async () => {
    setLoading(true);
    try {
      console.log('🧪 Testing business-specific Q&A generation...');
      
      const questionsAndResponses = await aiService.generateBusinessQuestionsAndResponses(
        businessInfo,
        documents,
        selectedProvider
      );

      setGeneratedQAs(questionsAndResponses);
      
      toast({
        title: "Success!",
        description: `Generated ${questionsAndResponses.length} business-specific Q&As`,
      });

    } catch (error: any) {
      console.error('Error generating Q&As:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to generate Q&As",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const editBusinessInfo = (field: string, value: any) => {
    setBusinessInfo(prev => ({ ...prev, [field]: value }));
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Brain className="h-5 w-5" />
            Business Training Demo
          </CardTitle>
          <CardDescription>
            See how AI generates responses based on your actual business information
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-4">
            <select
              value={selectedProvider}
              onChange={(e) => setSelectedProvider(e.target.value)}
              className="border rounded px-3 py-2"
            >
              {availableProviders.map(provider => (
                <option key={provider} value={provider}>
                  {provider === 'openai' ? 'OpenAI GPT-3.5' :
                   provider === 'gemini' ? 'Google Gemini (Auto-detect Model)' :
                   provider === 'mock' ? 'Mock AI' : provider}
                </option>
              ))}
            </select>
            
            <Button onClick={handleGenerateQAs} disabled={loading}>
              {loading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Generating...
                </>
              ) : (
                <>
                  <Zap className="mr-2 h-4 w-4" />
                  Generate Business Q&As
                </>
              )}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Sample Business Info */}
      <Card>
        <CardHeader>
          <CardTitle>Sample Business Information</CardTitle>
          <CardDescription>
            This is the business data that will be used to generate responses
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="text-sm font-medium">Business Name:</label>
              <input 
                value={businessInfo.business_name}
                onChange={(e) => editBusinessInfo('business_name', e.target.value)}
                className="w-full border rounded px-3 py-2 mt-1"
              />
            </div>
            <div>
              <label className="text-sm font-medium">Phone:</label>
              <input 
                value={businessInfo.phone}
                onChange={(e) => editBusinessInfo('phone', e.target.value)}
                className="w-full border rounded px-3 py-2 mt-1"
              />
            </div>
          </div>
          
          <div>
            <label className="text-sm font-medium">Description:</label>
            <Textarea 
              value={businessInfo.description}
              onChange={(e) => editBusinessInfo('description', e.target.value)}
              className="mt-1"
              rows={3}
            />
          </div>

          <div>
            <label className="text-sm font-medium">Services:</label>
            <div className="flex flex-wrap gap-2 mt-1">
              {businessInfo.services.map((service, index) => (
                <Badge key={index} variant="outline">{service}</Badge>
              ))}
            </div>
          </div>

          <div>
            <label className="text-sm font-medium">Sample Document (Menu):</label>
            <div className="bg-gray-50 p-3 rounded text-sm mt-1">
              {documents[0].content}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Generated Q&As */}
      {generatedQAs.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5 text-green-500" />
              Generated Questions & Responses
            </CardTitle>
            <CardDescription>
              AI-generated Q&As based on your business information
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {generatedQAs.map((qa, index) => (
                <div key={index} className="border rounded-lg p-4">
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex-1">
                      <h4 className="font-medium text-sm flex items-center gap-2">
                        <MessageSquare className="h-4 w-4" />
                        {qa.question}
                      </h4>
                      <Badge variant="outline" className="text-xs mt-1">
                        {qa.intent}
                      </Badge>
                    </div>
                  </div>
                  
                  <div className="bg-blue-50 p-3 rounded text-sm">
                    <strong>AI Response:</strong><br />
                    {qa.response}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Instructions */}
      <Card>
        <CardHeader>
          <CardTitle>How This Works</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3 text-sm">
            <div className="flex items-start gap-3">
              <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0">
                <span className="text-blue-600 font-bold text-xs">1</span>
              </div>
              <div>
                <strong>Business Context:</strong> The AI receives your complete business information including name, description, hours, services, contact details, and any uploaded documents.
              </div>
            </div>
            
            <div className="flex items-start gap-3">
              <div className="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0">
                <span className="text-green-600 font-bold text-xs">2</span>
              </div>
              <div>
                <strong>AI Generation:</strong> The AI analyzes your business data and generates realistic customer questions and accurate responses specific to your business.
              </div>
            </div>
            
            <div className="flex items-start gap-3">
              <div className="w-6 h-6 bg-purple-100 rounded-full flex items-center justify-center flex-shrink-0">
                <span className="text-purple-600 font-bold text-xs">3</span>
              </div>
              <div>
                <strong>Customization:</strong> You can review, edit, and approve each response before your bot uses them with customers.
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
