import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useBots, BotCreateData } from '@/hooks/useBots';
import { useToast } from '@/hooks/use-toast';
import { Loader2, MessageSquare, Phone, Facebook } from 'lucide-react';

interface CreateBotFormProps {
  onSuccess?: (bot: any) => void;
  onCancel?: () => void;
}

export const CreateBotForm: React.FC<CreateBotFormProps> = ({ onSuccess, onCancel }) => {
  const [formData, setFormData] = useState<BotCreateData>({
    name: '',
    channel: 'whatsapp',
    description: '',
    tone: '',
    language: 'English'
  });
  const [loading, setLoading] = useState(false);
  const { createBot } = useBots();
  const { toast } = useToast();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.name.trim()) {
      toast({
        title: "Validation Error",
        description: "Bot name is required",
        variant: "destructive",
      });
      return;
    }

    setLoading(true);
    try {
      const newBot = await createBot(formData);
      if (newBot) {
        // Reset form
        setFormData({
          name: '',
          channel: 'whatsapp',
          description: '',
          tone: '',
          language: 'English'
        });
        
        onSuccess?.(newBot);
      }
    } catch (error) {
      console.error('Form submission error:', error);
    } finally {
      setLoading(false);
    }
  };

  const getChannelIcon = (channel: string) => {
    switch (channel) {
      case 'whatsapp':
        return <MessageSquare className="h-4 w-4" />;
      case 'sms':
        return <Phone className="h-4 w-4" />;
      case 'messenger':
        return <Facebook className="h-4 w-4" />;
      default:
        return <MessageSquare className="h-4 w-4" />;
    }
  };

  return (
    <Card className="w-full max-w-2xl">
      <CardHeader>
        <CardTitle>Create New Bot</CardTitle>
        <CardDescription>
          Set up your AI bot to handle customer interactions across different channels
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Bot Name */}
          <div className="space-y-2">
            <Label htmlFor="name">Bot Name *</Label>
            <Input
              id="name"
              type="text"
              placeholder="e.g., Customer Support Bot"
              value={formData.name}
              onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
              disabled={loading}
              required
            />
          </div>

          {/* Channel Selection */}
          <div className="space-y-2">
            <Label htmlFor="channel">Channel *</Label>
            <Select
              value={formData.channel}
              onValueChange={(value: 'whatsapp' | 'sms' | 'messenger') => 
                setFormData(prev => ({ ...prev, channel: value }))
              }
              disabled={loading}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select a channel" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="whatsapp">
                  <div className="flex items-center gap-2">
                    {getChannelIcon('whatsapp')}
                    WhatsApp
                  </div>
                </SelectItem>
                <SelectItem value="sms">
                  <div className="flex items-center gap-2">
                    {getChannelIcon('sms')}
                    SMS
                  </div>
                </SelectItem>
                <SelectItem value="messenger">
                  <div className="flex items-center gap-2">
                    {getChannelIcon('messenger')}
                    Facebook Messenger
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Description */}
          <div className="space-y-2">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              placeholder="Describe what this bot will do..."
              value={formData.description}
              onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              disabled={loading}
              rows={3}
            />
          </div>

          {/* Tone */}
          <div className="space-y-2">
            <Label htmlFor="tone">Tone</Label>
            <Select
              value={formData.tone}
              onValueChange={(value) => setFormData(prev => ({ ...prev, tone: value }))}
              disabled={loading}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select bot tone" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="professional">Professional</SelectItem>
                <SelectItem value="friendly">Friendly</SelectItem>
                <SelectItem value="casual">Casual</SelectItem>
                <SelectItem value="formal">Formal</SelectItem>
                <SelectItem value="enthusiastic">Enthusiastic</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Language */}
          <div className="space-y-2">
            <Label htmlFor="language">Language</Label>
            <Select
              value={formData.language}
              onValueChange={(value) => setFormData(prev => ({ ...prev, language: value }))}
              disabled={loading}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select language" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="English">English</SelectItem>
                <SelectItem value="Spanish">Spanish</SelectItem>
                <SelectItem value="French">French</SelectItem>
                <SelectItem value="German">German</SelectItem>
                <SelectItem value="Arabic">Arabic</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Form Actions */}
          <div className="flex justify-end space-x-4 pt-4">
            {onCancel && (
              <Button
                type="button"
                variant="outline"
                onClick={onCancel}
                disabled={loading}
              >
                Cancel
              </Button>
            )}
            <Button type="submit" disabled={loading}>
              {loading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Creating Bot...
                </>
              ) : (
                'Create Bot'
              )}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
};
