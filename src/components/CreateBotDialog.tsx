import React, { useState } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { CreateBotForm } from './CreateBotForm';
import { Plus } from 'lucide-react';

interface CreateBotDialogProps {
  trigger?: React.ReactNode;
  onBotCreated?: (bot: any) => void;
}

export const CreateBotDialog: React.FC<CreateBotDialogProps> = ({ 
  trigger, 
  onBotCreated 
}) => {
  const [open, setOpen] = useState(false);

  const handleSuccess = (bot: any) => {
    setOpen(false);
    onBotCreated?.(bot);
  };

  const handleCancel = () => {
    setOpen(false);
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {trigger || (
          <Button>
            <Plus className="mr-2 h-4 w-4" />
            Create Bot
          </Button>
        )}
      </DialogTrigger>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Create New Bot</DialogTitle>
          <DialogDescription>
            Set up your AI bot to handle customer interactions across different channels
          </DialogDescription>
        </DialogHeader>
        <CreateBotForm 
          onSuccess={handleSuccess}
          onCancel={handleCancel}
        />
      </DialogContent>
    </Dialog>
  );
};
