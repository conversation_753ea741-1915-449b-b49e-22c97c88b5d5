import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { AIChatInterface } from './AIChatInterface';
import { CreateBotDialog } from './CreateBotDialog';
import { useBots } from '@/hooks/useBots';
import { useAuth } from '@/contexts/AuthContext';
import { aiService } from '@/services/aiService';
import { Bot, MessageSquare, Zap, Settings, Brain } from 'lucide-react';

export const AIChatDemo: React.FC = () => {
  const [selectedBotId, setSelectedBotId] = useState<string>('');
  const [selectedProvider, setSelectedProvider] = useState<string>('');
  const { bots, loading, getActiveBots } = useBots();
  const { user } = useAuth();

  const activeBots = getActiveBots();
  const availableProviders = aiService.getAvailableProviders();

  useEffect(() => {
    // Auto-select first active bot
    if (activeBots.length > 0 && !selectedBotId) {
      setSelectedBotId(activeBots[0].id);
    }
  }, [activeBots, selectedBotId]);

  useEffect(() => {
    // Auto-select first available provider
    if (availableProviders.length > 0 && !selectedProvider) {
      // Prefer real AI providers over mock
      const preferredProvider = availableProviders.find(p => p !== 'mock') || availableProviders[0];
      setSelectedProvider(preferredProvider);
      aiService.setDefaultProvider(preferredProvider);
    }
  }, [availableProviders, selectedProvider]);

  const handleProviderChange = (provider: string) => {
    setSelectedProvider(provider);
    aiService.setDefaultProvider(provider);
  };

  const handleBotCreated = (bot: any) => {
    setSelectedBotId(bot.id);
  };

  if (!user) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="text-center">
            <Bot className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
            <p className="text-muted-foreground">
              Please log in to test AI chat functionality
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold flex items-center gap-2">
            <MessageSquare className="h-6 w-6" />
            AI Chat Demo
          </h2>
          <p className="text-muted-foreground">
            Test your AI-powered chat bots with real conversations
          </p>
        </div>
        <CreateBotDialog onBotCreated={handleBotCreated} />
      </div>

      {/* Features */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center gap-2 mb-2">
              <Zap className="h-5 w-5 text-yellow-500" />
              <h3 className="font-semibold">AI-Powered Responses</h3>
            </div>
            <p className="text-sm text-muted-foreground">
              Get intelligent responses powered by advanced AI models
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center gap-2 mb-2">
              <MessageSquare className="h-5 w-5 text-blue-500" />
              <h3 className="font-semibold">Multi-Channel Support</h3>
            </div>
            <p className="text-sm text-muted-foreground">
              Test conversations across WhatsApp, SMS, and Messenger
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center gap-2 mb-2">
              <Settings className="h-5 w-5 text-green-500" />
              <h3 className="font-semibold">Context Awareness</h3>
            </div>
            <p className="text-sm text-muted-foreground">
              Maintains conversation context and bot personality
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Bot Selection */}
      <Card>
        <CardHeader>
          <CardTitle>Select Bot to Test</CardTitle>
          <CardDescription>
            Choose an active bot to start testing AI conversations
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <p className="text-muted-foreground">Loading bots...</p>
          ) : activeBots.length === 0 ? (
            <div className="text-center py-8">
              <Bot className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
              <p className="text-muted-foreground mb-4">No active bots found</p>
              <CreateBotDialog 
                trigger={<Button>Create Your First Bot</Button>}
                onBotCreated={handleBotCreated}
              />
            </div>
          ) : (
            <div className="space-y-4">
              <div className="flex items-center gap-4">
                <Select value={selectedBotId} onValueChange={setSelectedBotId}>
                  <SelectTrigger className="w-[300px]">
                    <SelectValue placeholder="Select a bot" />
                  </SelectTrigger>
                  <SelectContent>
                    {activeBots.map((bot) => (
                      <SelectItem key={bot.id} value={bot.id}>
                        <div className="flex items-center gap-2">
                          <Bot className="h-4 w-4" />
                          <span>{bot.name}</span>
                          <Badge variant="outline" className="ml-2">
                            {bot.channel}
                          </Badge>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                
                {selectedBotId && (
                  <Badge variant="secondary">
                    Ready to chat
                  </Badge>
                )}
              </div>
              
              {selectedBotId && (
                <div className="text-sm text-muted-foreground">
                  <p>
                    <strong>Selected Bot:</strong> {activeBots.find(b => b.id === selectedBotId)?.name}
                  </p>
                  <p>
                    <strong>Channel:</strong> {activeBots.find(b => b.id === selectedBotId)?.channel}
                  </p>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Chat Interface */}
      {selectedBotId && (
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <div className="lg:col-span-2">
            <AIChatInterface botId={selectedBotId} />
          </div>
          
          <div className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Chat Tips</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div>
                  <h4 className="font-medium text-sm">Try these prompts:</h4>
                  <ul className="text-sm text-muted-foreground mt-1 space-y-1">
                    <li>• "Hello, how can you help me?"</li>
                    <li>• "What services do you offer?"</li>
                    <li>• "I need help with my order"</li>
                    <li>• "Can you tell me about pricing?"</li>
                  </ul>
                </div>
                
                <div>
                  <h4 className="font-medium text-sm">Features:</h4>
                  <ul className="text-sm text-muted-foreground mt-1 space-y-1">
                    <li>• Real-time AI responses</li>
                    <li>• Conversation memory</li>
                    <li>• Multi-channel support</li>
                    <li>• Context awareness</li>
                  </ul>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center gap-2">
                  <Brain className="h-5 w-5" />
                  AI Provider
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <label className="text-sm font-medium">Select Provider:</label>
                    <Select value={selectedProvider} onValueChange={handleProviderChange}>
                      <SelectTrigger className="w-full mt-1">
                        <SelectValue placeholder="Select AI provider" />
                      </SelectTrigger>
                      <SelectContent>
                        {availableProviders.map((provider) => (
                          <SelectItem key={provider} value={provider}>
                            <div className="flex items-center gap-2">
                              <Brain className="h-4 w-4" />
                              {provider === 'openai' ? 'OpenAI GPT-3.5' :
                               provider === 'gemini' ? 'Google Gemini Pro' :
                               provider === 'mock' ? 'Mock AI (Demo)' : provider}
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Badge variant="outline">
                      {selectedProvider === 'openai' ? 'OpenAI GPT-3.5' :
                       selectedProvider === 'gemini' ? 'Google Gemini Pro' :
                       selectedProvider === 'mock' ? 'Mock AI (Demo)' : selectedProvider}
                    </Badge>
                    <p className="text-xs text-muted-foreground">
                      {selectedProvider === 'openai' && 'Using OpenAI GPT-3.5 for intelligent responses'}
                      {selectedProvider === 'gemini' && 'Using Google Gemini Pro for advanced AI responses'}
                      {selectedProvider === 'mock' && 'Using mock AI for demo purposes'}
                    </p>
                  </div>

                  <div className="text-xs text-muted-foreground">
                    <p><strong>Available:</strong></p>
                    <ul className="mt-1 space-y-1">
                      <li>• OpenAI: {import.meta.env.VITE_OPENAI_API_KEY ? '✅ Configured' : '❌ No API key'}</li>
                      <li>• Gemini: {import.meta.env.VITE_GEMINI_API_KEY ? '✅ Configured' : '❌ No API key'}</li>
                      <li>• Mock AI: ✅ Always available</li>
                    </ul>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      )}
    </div>
  );
};
