import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { AIChatDemo } from './AIChatDemo';
import { AIAnalyticsDashboard } from './AIAnalyticsDashboard';
import { AIChatTester } from './AIChatTester';
import { useAuth } from '@/contexts/AuthContext';
import { aiService } from '@/services/aiService';
import { 
  MessageSquare, 
  BarChart3, 
  TestTube, 
  Brain,
  Zap,
  Database,
  Settings
} from 'lucide-react';

export const ComprehensiveAIDemo: React.FC = () => {
  const { user } = useAuth();
  const availableProviders = aiService.getAvailableProviders();

  const getProviderStatus = () => {
    return {
      openai: !!import.meta.env.VITE_OPENAI_API_KEY,
      gemini: !!import.meta.env.VITE_GEMINI_API_KEY,
      mock: true,
    };
  };

  const providerStatus = getProviderStatus();

  if (!user) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="text-center">
            <Brain className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
            <p className="text-muted-foreground">
              Please log in to access the AI system
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center space-y-4">
        <h1 className="text-3xl font-bold flex items-center justify-center gap-2">
          <Brain className="h-8 w-8" />
          AI-Powered Chat System
        </h1>
        <p className="text-muted-foreground max-w-2xl mx-auto">
          Complete AI integration with OpenAI and Google Gemini, featuring real-time chat, 
          analytics tracking, and comprehensive response management.
        </p>
      </div>

      {/* System Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            System Status
          </CardTitle>
          <CardDescription>
            Current AI provider configuration and availability
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="flex items-center justify-between p-3 border rounded-lg">
              <div className="flex items-center gap-2">
                <Zap className="h-5 w-5 text-green-500" />
                <span className="font-medium">OpenAI GPT-3.5</span>
              </div>
              <Badge variant={providerStatus.openai ? "default" : "secondary"}>
                {providerStatus.openai ? "✅ Active" : "❌ No API Key"}
              </Badge>
            </div>
            
            <div className="flex items-center justify-between p-3 border rounded-lg">
              <div className="flex items-center gap-2">
                <Brain className="h-5 w-5 text-blue-500" />
                <span className="font-medium">Google Gemini Pro</span>
              </div>
              <Badge variant={providerStatus.gemini ? "default" : "secondary"}>
                {providerStatus.gemini ? "✅ Active" : "❌ No API Key"}
              </Badge>
            </div>
            
            <div className="flex items-center justify-between p-3 border rounded-lg">
              <div className="flex items-center gap-2">
                <TestTube className="h-5 w-5 text-purple-500" />
                <span className="font-medium">Mock AI</span>
              </div>
              <Badge variant="outline">
                ✅ Always Available
              </Badge>
            </div>
          </div>
          
          <div className="mt-4 p-3 bg-blue-50 rounded-lg">
            <p className="text-sm text-blue-800">
              <strong>Available Providers:</strong> {availableProviders.length} 
              ({availableProviders.join(', ')})
            </p>
            <p className="text-xs text-blue-600 mt-1">
              All responses are automatically tracked in Supabase with metadata including 
              provider, tokens used, processing time, and costs.
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Main Tabs */}
      <Tabs defaultValue="chat" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="chat" className="flex items-center gap-2">
            <MessageSquare className="h-4 w-4" />
            AI Chat
          </TabsTrigger>
          <TabsTrigger value="analytics" className="flex items-center gap-2">
            <BarChart3 className="h-4 w-4" />
            Analytics
          </TabsTrigger>
          <TabsTrigger value="testing" className="flex items-center gap-2">
            <TestTube className="h-4 w-4" />
            Testing
          </TabsTrigger>
        </TabsList>

        <TabsContent value="chat" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Interactive AI Chat</CardTitle>
              <CardDescription>
                Chat with your bots using OpenAI, Gemini, or Mock AI providers
              </CardDescription>
            </CardHeader>
            <CardContent>
              <AIChatDemo />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>AI Usage Analytics</CardTitle>
              <CardDescription>
                Track usage, costs, and performance across all AI providers
              </CardDescription>
            </CardHeader>
            <CardContent>
              <AIAnalyticsDashboard />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="testing" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>AI Service Tester</CardTitle>
                <CardDescription>
                  Test AI providers directly without creating messages
                </CardDescription>
              </CardHeader>
              <CardContent>
                <AIChatTester />
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader>
                <CardTitle>Database Integration</CardTitle>
                <CardDescription>
                  How the system integrates with Supabase
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center gap-2">
                  <Database className="h-5 w-5 text-green-500" />
                  <span className="font-medium">Supabase Integration</span>
                </div>
                
                <div className="space-y-3 text-sm">
                  <div>
                    <h4 className="font-medium">Tables Used:</h4>
                    <ul className="mt-1 space-y-1 text-muted-foreground">
                      <li>• <code>messages</code> - Stores all chat messages</li>
                      <li>• <code>ai_responses</code> - Tracks AI metadata</li>
                      <li>• <code>bots</code> - Bot configurations</li>
                    </ul>
                  </div>
                  
                  <div>
                    <h4 className="font-medium">Tracked Metadata:</h4>
                    <ul className="mt-1 space-y-1 text-muted-foreground">
                      <li>• Provider used (OpenAI, Gemini, Mock)</li>
                      <li>• Tokens consumed</li>
                      <li>• Processing time</li>
                      <li>• Estimated costs</li>
                      <li>• Response quality ratings</li>
                    </ul>
                  </div>
                  
                  <div>
                    <h4 className="font-medium">Features:</h4>
                    <ul className="mt-1 space-y-1 text-muted-foreground">
                      <li>• Real-time response generation</li>
                      <li>• Conversation context awareness</li>
                      <li>• Multi-provider support</li>
                      <li>• Cost tracking and analytics</li>
                      <li>• Quality rating system</li>
                    </ul>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>

      {/* Setup Instructions */}
      <Card>
        <CardHeader>
          <CardTitle>Setup Instructions</CardTitle>
          <CardDescription>
            How to configure AI providers for production use
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-medium mb-2">OpenAI Setup:</h4>
              <ol className="text-sm space-y-1 text-muted-foreground">
                <li>1. Get API key from <a href="https://platform.openai.com/" target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">OpenAI Platform</a></li>
                <li>2. Add to .env: <code>VITE_OPENAI_API_KEY=your-key</code></li>
                <li>3. Restart development server</li>
              </ol>
            </div>
            
            <div>
              <h4 className="font-medium mb-2">Gemini Setup:</h4>
              <ol className="text-sm space-y-1 text-muted-foreground">
                <li>1. Get API key from <a href="https://makersuite.google.com/app/apikey" target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">Google AI Studio</a></li>
                <li>2. Add to .env: <code>VITE_GEMINI_API_KEY=your-key</code></li>
                <li>3. Restart development server</li>
              </ol>
            </div>
          </div>
          
          <div className="p-3 bg-yellow-50 rounded-lg">
            <p className="text-sm text-yellow-800">
              <strong>Note:</strong> Run the SQL migration in <code>supabase_ai_responses_migration.sql</code> 
              in your Supabase SQL editor to create the required database tables.
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
