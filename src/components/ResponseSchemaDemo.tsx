import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ResponseSchemaManager } from './ResponseSchemaManager';
import { AIChatDemo } from './AIChatDemo';
import { useAuth } from '@/contexts/AuthContext';
import { 
  Target, 
  MessageSquare, 
  Brain,
  CheckCircle,
  Lightbulb,
  Database
} from 'lucide-react';

export const ResponseSchemaDemo: React.FC = () => {
  const { user } = useAuth();

  if (!user) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="text-center">
            <Target className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
            <p className="text-muted-foreground">
              Please log in to access the Response Schema System
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center space-y-4">
        <h1 className="text-3xl font-bold flex items-center justify-center gap-2">
          <Target className="h-8 w-8" />
          AI Response Schema System
        </h1>
        <p className="text-muted-foreground max-w-3xl mx-auto">
          Generate and manage AI responses for common customer questions based on your business type. 
          Perfect for restaurants, retail stores, and other businesses with predictable customer inquiries.
        </p>
      </div>

      {/* How It Works */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Lightbulb className="h-5 w-5" />
            How It Works
          </CardTitle>
          <CardDescription>
            The Response Schema System helps you prepare your bot for common customer questions
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="text-center p-4 border rounded-lg">
              <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
                <span className="text-blue-600 font-bold">1</span>
              </div>
              <h3 className="font-medium mb-2">Select Business Type</h3>
              <p className="text-sm text-muted-foreground">
                Choose your business category (restaurant, retail, etc.)
              </p>
            </div>
            
            <div className="text-center p-4 border rounded-lg">
              <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3">
                <span className="text-green-600 font-bold">2</span>
              </div>
              <h3 className="font-medium mb-2">Generate Responses</h3>
              <p className="text-sm text-muted-foreground">
                AI generates responses for common questions in your industry
              </p>
            </div>
            
            <div className="text-center p-4 border rounded-lg">
              <div className="w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-3">
                <span className="text-orange-600 font-bold">3</span>
              </div>
              <h3 className="font-medium mb-2">Review & Customize</h3>
              <p className="text-sm text-muted-foreground">
                Edit responses to match your business specifics
              </p>
            </div>
            
            <div className="text-center p-4 border rounded-lg">
              <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-3">
                <span className="text-purple-600 font-bold">4</span>
              </div>
              <h3 className="font-medium mb-2">Deploy & Use</h3>
              <p className="text-sm text-muted-foreground">
                Your bot uses these responses for customer inquiries
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Example Questions by Business Type */}
      <Card>
        <CardHeader>
          <CardTitle>Example Questions by Business Type</CardTitle>
          <CardDescription>
            See what types of questions the system can help you prepare for
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div>
              <h4 className="font-medium mb-3 flex items-center gap-2">
                🍽️ Restaurant
              </h4>
              <ul className="space-y-2 text-sm text-muted-foreground">
                <li>• "What cuisines do you have?"</li>
                <li>• "Can I see your menu?"</li>
                <li>• "What are your opening hours?"</li>
                <li>• "Do you deliver?"</li>
                <li>• "Can I make a reservation?"</li>
                <li>• "Do you have vegetarian options?"</li>
              </ul>
            </div>
            
            <div>
              <h4 className="font-medium mb-3 flex items-center gap-2">
                🛍️ Retail
              </h4>
              <ul className="space-y-2 text-sm text-muted-foreground">
                <li>• "What products do you sell?"</li>
                <li>• "Do you have this in stock?"</li>
                <li>• "What are your return policies?"</li>
                <li>• "Do you offer shipping?"</li>
                <li>• "What payment methods do you accept?"</li>
                <li>• "Do you have a loyalty program?"</li>
              </ul>
            </div>
            
            <div>
              <h4 className="font-medium mb-3 flex items-center gap-2">
                🏥 Healthcare
              </h4>
              <ul className="space-y-2 text-sm text-muted-foreground">
                <li>• "How do I book an appointment?"</li>
                <li>• "What services do you offer?"</li>
                <li>• "Do you accept my insurance?"</li>
                <li>• "What are your office hours?"</li>
                <li>• "How do I get my test results?"</li>
                <li>• "Can I reschedule my appointment?"</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Main Tabs */}
      <Tabs defaultValue="manager" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="manager" className="flex items-center gap-2">
            <Target className="h-4 w-4" />
            Schema Manager
          </TabsTrigger>
          <TabsTrigger value="testing" className="flex items-center gap-2">
            <MessageSquare className="h-4 w-4" />
            Test Chat
          </TabsTrigger>
        </TabsList>

        <TabsContent value="manager" className="space-y-4">
          <ResponseSchemaManager />
        </TabsContent>

        <TabsContent value="testing" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Test Your Response Schemas</CardTitle>
              <CardDescription>
                Chat with your bot to see how it uses the generated response schemas
              </CardDescription>
            </CardHeader>
            <CardContent>
              <AIChatDemo />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Benefits */}
      <Card>
        <CardHeader>
          <CardTitle>Benefits of Response Schemas</CardTitle>
          <CardDescription>
            Why this approach is better than generating responses on-the-fly
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div className="flex items-start gap-3">
                <CheckCircle className="h-5 w-5 text-green-500 mt-0.5" />
                <div>
                  <h4 className="font-medium">Consistent Responses</h4>
                  <p className="text-sm text-muted-foreground">
                    Ensure your bot always gives accurate, brand-consistent answers
                  </p>
                </div>
              </div>
              
              <div className="flex items-start gap-3">
                <CheckCircle className="h-5 w-5 text-green-500 mt-0.5" />
                <div>
                  <h4 className="font-medium">Cost Effective</h4>
                  <p className="text-sm text-muted-foreground">
                    Generate responses once, use them many times without API costs
                  </p>
                </div>
              </div>
              
              <div className="flex items-start gap-3">
                <CheckCircle className="h-5 w-5 text-green-500 mt-0.5" />
                <div>
                  <h4 className="font-medium">Quality Control</h4>
                  <p className="text-sm text-muted-foreground">
                    Review and approve all responses before customers see them
                  </p>
                </div>
              </div>
            </div>
            
            <div className="space-y-4">
              <div className="flex items-start gap-3">
                <CheckCircle className="h-5 w-5 text-green-500 mt-0.5" />
                <div>
                  <h4 className="font-medium">Faster Response Times</h4>
                  <p className="text-sm text-muted-foreground">
                    Pre-generated responses are delivered instantly
                  </p>
                </div>
              </div>
              
              <div className="flex items-start gap-3">
                <CheckCircle className="h-5 w-5 text-green-500 mt-0.5" />
                <div>
                  <h4 className="font-medium">Business-Specific</h4>
                  <p className="text-sm text-muted-foreground">
                    Responses tailored to your specific business and industry
                  </p>
                </div>
              </div>
              
              <div className="flex items-start gap-3">
                <CheckCircle className="h-5 w-5 text-green-500 mt-0.5" />
                <div>
                  <h4 className="font-medium">Easy Management</h4>
                  <p className="text-sm text-muted-foreground">
                    Update and manage all responses from one central location
                  </p>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Setup Instructions */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Database className="h-5 w-5" />
            Setup Instructions
          </CardTitle>
          <CardDescription>
            How to set up the Response Schema System
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="p-4 bg-blue-50 rounded-lg">
            <h4 className="font-medium mb-2">Database Setup Required</h4>
            <p className="text-sm text-blue-800 mb-3">
              Run the SQL migration in your Supabase dashboard to create the required tables:
            </p>
            <ol className="text-sm text-blue-700 space-y-1">
              <li>1. Go to your Supabase dashboard → SQL Editor</li>
              <li>2. Copy and paste the content from <code>supabase_ai_responses_migration.sql</code></li>
              <li>3. Click "Run" to create the tables and sample data</li>
              <li>4. Refresh this page and start generating response schemas!</li>
            </ol>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h4 className="font-medium mb-2">Tables Created:</h4>
              <ul className="text-sm text-muted-foreground space-y-1">
                <li>• <code>business_categories</code> - Business types</li>
                <li>• <code>question_templates</code> - Common questions</li>
                <li>• <code>bot_response_schemas</code> - Generated responses</li>
              </ul>
            </div>
            
            <div>
              <h4 className="font-medium mb-2">Features Included:</h4>
              <ul className="text-sm text-muted-foreground space-y-1">
                <li>• Pre-loaded restaurant questions</li>
                <li>• Multi-provider AI support</li>
                <li>• Response approval workflow</li>
                <li>• Coverage tracking</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
