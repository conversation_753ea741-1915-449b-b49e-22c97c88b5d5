import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { createDemoAccount, testDemoLogin } from '@/utils/createDemoAccount';
import { useToast } from '@/hooks/use-toast';

export const DemoAccountHelper: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const { toast } = useToast();

  const handleCreateDemo = async () => {
    setLoading(true);
    try {
      const result = await createDemoAccount();
      
      if (result.success) {
        toast({
          title: "Success",
          description: "Demo account created successfully! You can now <NAME_EMAIL> / demo123",
        });
      } else {
        toast({
          title: "Error",
          description: result.error || "Failed to create demo account",
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "An unexpected error occurred",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleTestLogin = async () => {
    setLoading(true);
    try {
      const result = await testDemoLogin();
      
      if (result.success) {
        toast({
          title: "Success",
          description: "Demo login test successful!",
        });
      } else {
        toast({
          title: "Login Test Failed",
          description: result.error || "Demo login failed",
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "An unexpected error occurred during login test",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Card className="w-full max-w-md">
      <CardHeader>
        <CardTitle>Demo Account Helper</CardTitle>
        <CardDescription>
          Create and test the demo account for development
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <p className="text-sm text-muted-foreground">
            <strong>Demo Credentials:</strong><br />
            Email: <EMAIL><br />
            Password: demo123
          </p>
        </div>
        
        <div className="flex flex-col space-y-2">
          <Button 
            onClick={handleCreateDemo} 
            disabled={loading}
            variant="outline"
          >
            {loading ? "Creating..." : "Create Demo Account"}
          </Button>
          
          <Button 
            onClick={handleTestLogin} 
            disabled={loading}
            variant="secondary"
          >
            {loading ? "Testing..." : "Test Demo Login"}
          </Button>
        </div>
        
        <div className="text-xs text-muted-foreground">
          <p>
            <strong>Note:</strong> This helper is for development only. 
            Remove it before production deployment.
          </p>
        </div>
      </CardContent>
    </Card>
  );
};
