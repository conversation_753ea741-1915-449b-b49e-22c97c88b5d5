import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { useToast } from '@/hooks/use-toast';
import { useResponseSchemas } from '@/hooks/useResponseSchemas';
import { aiService } from '@/services/aiService';
import { supabase } from '@/integrations/supabase/client';
import { BusinessInfo } from './BusinessInfoForm';
import { 
  Brain, 
  Zap, 
  FileText, 
  MessageSquare, 
  CheckCircle, 
  XCircle,
  Loader2,
  RefreshCw,
  Edit3,
  Save
} from 'lucide-react';

interface AITrainingManagerProps {
  botId: string;
  businessInfo?: BusinessInfo;
  documents?: any[];
  onTrainingComplete?: () => void;
}

export const AITrainingManager: React.FC<AITrainingManagerProps> = ({
  botId,
  businessInfo,
  documents = [],
  onTrainingComplete
}) => {
  const [selectedProvider, setSelectedProvider] = useState('openai');
  const [trainingStatus, setTrainingStatus] = useState<'idle' | 'generating' | 'complete'>('idle');
  const [progress, setProgress] = useState(0);
  const [editingResponse, setEditingResponse] = useState<string | null>(null);
  const [editText, setEditText] = useState('');

  const { toast } = useToast();
  const {
    responseSchemas,
    botCoverage,
    loading,
    generating,
    generateResponseSchemas,
    updateResponseSchema,
    refetch
  } = useResponseSchemas(botId);

  const availableProviders = aiService.getAvailableProviders();

  useEffect(() => {
    console.log('🔍 Available AI providers:', availableProviders);
    if (availableProviders.length > 0 && !selectedProvider) {
      const preferredProvider = availableProviders.find(p => p !== 'mock') || availableProviders[0];
      console.log('🎯 Setting default provider to:', preferredProvider);
      setSelectedProvider(preferredProvider);
    }
  }, [availableProviders]);

  const buildBusinessContext = (): string => {
    if (!businessInfo) return '';

    let context = `Business: ${businessInfo.business_name}\n`;
    context += `Type: ${businessInfo.business_type}\n`;
    
    if (businessInfo.description) {
      context += `Description: ${businessInfo.description}\n`;
    }
    
    if (businessInfo.address) {
      context += `Address: ${businessInfo.address}\n`;
    }
    
    if (businessInfo.phone) {
      context += `Phone: ${businessInfo.phone}\n`;
    }
    
    if (businessInfo.email) {
      context += `Email: ${businessInfo.email}\n`;
    }
    
    if (businessInfo.website) {
      context += `Website: ${businessInfo.website}\n`;
    }

    // Add hours
    context += `\nBusiness Hours:\n`;
    Object.entries(businessInfo.hours).forEach(([day, hours]) => {
      context += `${day.charAt(0).toUpperCase() + day.slice(1)}: ${hours}\n`;
    });

    // Add services
    if (businessInfo.services.length > 0) {
      context += `\nServices/Products: ${businessInfo.services.join(', ')}\n`;
    }

    // Add special info
    if (businessInfo.special_info) {
      context += `\nSpecial Information: ${businessInfo.special_info}\n`;
    }

    return context;
  };

  const buildDocumentContext = (): string => {
    if (!documents || documents.length === 0) return '';

    let context = '\nDocument Information:\n';
    documents.forEach((doc, index) => {
      if (doc.type === 'file' && doc.content) {
        context += `Document ${index + 1} (${doc.file_name}): ${doc.content.substring(0, 500)}...\n`;
      }
    });

    return context;
  };

  const handleGenerateResponses = async () => {
    if (!businessInfo) {
      toast({
        title: "Missing Information",
        description: "Please fill in business information first",
        variant: "destructive",
      });
      return;
    }

    setTrainingStatus('generating');
    setProgress(10);

    try {
      console.log('🤖 Training bot with business-specific data...');
      console.log('Business Info:', businessInfo);
      console.log('Documents:', documents);

      // Step 1: Generate business-specific questions and responses using AI
      setProgress(30);
      const questionsAndResponses = await aiService.generateBusinessQuestionsAndResponses(
        businessInfo,
        documents,
        selectedProvider
      );

      console.log('Generated Q&As:', questionsAndResponses);

      if (questionsAndResponses.length === 0) {
        throw new Error('No questions and responses were generated');
      }

      setProgress(60);

      // Step 2: Store these as custom response schemas in the database
      const schemas = questionsAndResponses.map(qa => ({
        bot_id: botId,
        custom_question: qa.question,
        custom_intent: qa.intent,
        ai_generated_response: qa.response,
        provider_used: selectedProvider,
        cta_buttons: qa.cta ? JSON.stringify(qa.cta.buttons) : null,
        cta_type: qa.cta ? qa.cta.type : 'none',
        is_approved: false,
        is_active: true
      }));

      // First, clear existing custom schemas for this bot
      const { error: deleteError } = await supabase
        .from('bot_response_schemas')
        .delete()
        .eq('bot_id', botId)
        .is('question_template_id', null); // Only delete custom ones

      if (deleteError) {
        console.error('Error clearing existing schemas:', deleteError);
      }

      // Insert new custom schemas
      const { error: insertError } = await supabase
        .from('bot_response_schemas')
        .insert(schemas.map(schema => ({
          bot_id: schema.bot_id,
          question_template_id: null, // Custom question, not from template
          ai_generated_response: schema.ai_generated_response,
          provider_used: schema.provider_used,
          is_approved: schema.is_approved,
          is_active: schema.is_active,
          custom_question: schema.question_text,
          custom_intent: schema.question_intent
        })));

      if (insertError) {
        console.error('Error inserting schemas:', insertError);
        throw insertError;
      }

      setProgress(100);
      setTrainingStatus('complete');

      toast({
        title: "Training Complete!",
        description: `Generated ${questionsAndResponses.length} business-specific responses based on your information`,
      });

      // Refresh the response schemas
      await refetch();
      onTrainingComplete?.();

    } catch (error: any) {
      console.error('Training failed:', error);
      toast({
        title: "Training Failed",
        description: error.message || "Failed to generate AI responses",
        variant: "destructive",
      });
      setTrainingStatus('idle');
    }
  };

  const handleEditResponse = (schemaId: string, currentResponse: string) => {
    setEditingResponse(schemaId);
    setEditText(currentResponse);
  };

  const handleSaveEdit = async (schemaId: string) => {
    await updateResponseSchema(schemaId, {
      custom_response: editText,
      is_approved: true,
    });
    setEditingResponse(null);
    setEditText('');
  };

  const handleApproveResponse = async (schemaId: string) => {
    await updateResponseSchema(schemaId, { is_approved: true });
  };

  const getTrainingReadiness = () => {
    const hasBusinessInfo = businessInfo && businessInfo.business_name;
    const hasDocuments = documents && documents.length > 0;
    
    return {
      ready: hasBusinessInfo,
      businessInfo: hasBusinessInfo,
      documents: hasDocuments,
      score: (hasBusinessInfo ? 70 : 0) + (hasDocuments ? 30 : 0)
    };
  };

  const readiness = getTrainingReadiness();

  return (
    <div className="space-y-6">
      {/* Training Readiness */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Brain className="h-5 w-5" />
            AI Training Readiness
          </CardTitle>
          <CardDescription>
            Check if your bot has enough information for AI training
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Training Readiness</span>
              <span className="text-sm text-muted-foreground">{readiness.score}%</span>
            </div>
            <Progress value={readiness.score} className="h-2" />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="flex items-center gap-3 p-3 border rounded-lg">
              {readiness.businessInfo ? (
                <CheckCircle className="h-5 w-5 text-green-500" />
              ) : (
                <XCircle className="h-5 w-5 text-red-500" />
              )}
              <div>
                <p className="font-medium text-sm">Business Information</p>
                <p className="text-xs text-muted-foreground">
                  {readiness.businessInfo ? 'Complete' : 'Required for training'}
                </p>
              </div>
            </div>

            <div className="flex items-center gap-3 p-3 border rounded-lg">
              {readiness.documents ? (
                <CheckCircle className="h-5 w-5 text-green-500" />
              ) : (
                <FileText className="h-5 w-5 text-gray-400" />
              )}
              <div>
                <p className="font-medium text-sm">Documents ({documents.length})</p>
                <p className="text-xs text-muted-foreground">
                  {readiness.documents ? 'Uploaded' : 'Optional but recommended'}
                </p>
              </div>
            </div>
          </div>

          {readiness.ready && (
            <div className="space-y-4">
              <div className="flex items-center gap-4">
                <div className="flex flex-col gap-2">
                  <label className="text-sm font-medium">AI Provider:</label>
                  <Select
                    value={selectedProvider}
                    onValueChange={(value) => {
                      console.log('🔄 Provider changed from', selectedProvider, 'to', value);
                      setSelectedProvider(value);
                    }}
                  >
                    <SelectTrigger className="w-[250px]">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {availableProviders.map((provider) => (
                        <SelectItem key={provider} value={provider}>
                          {provider === 'openai' ? '🤖 OpenAI GPT-3.5' :
                           provider === 'gemini' ? '🧠 Google Gemini (Auto-detect)' :
                           provider === 'mock' ? '🎭 Mock AI (Testing)' : provider}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex-1">
                  <div className="text-xs text-muted-foreground">
                    {selectedProvider === 'openai' && '✨ Best for consistent, professional responses'}
                    {selectedProvider === 'gemini' && '🚀 Fast and cost-effective, auto-detects best model'}
                    {selectedProvider === 'mock' && '🧪 For testing without using API credits'}
                  </div>
                  <div className="text-xs text-gray-400 mt-1">
                    Current: {selectedProvider} | Available: {availableProviders.join(', ')}
                  </div>
                  <div className="flex gap-2 mt-2">
                    {availableProviders.map(provider => (
                      <button
                        key={provider}
                        onClick={() => {
                          console.log('🔘 Direct button click:', provider);
                          setSelectedProvider(provider);
                        }}
                        className={`text-xs px-2 py-1 rounded border ${
                          selectedProvider === provider
                            ? 'bg-blue-100 border-blue-300'
                            : 'bg-gray-100 border-gray-300'
                        }`}
                      >
                        {provider}
                      </button>
                    ))}
                  </div>
                </div>
              </div>

              <div className="flex flex-col gap-2 flex-1">
                <Button
                  onClick={handleGenerateResponses}
                  disabled={generating || trainingStatus === 'generating'}
                  className="w-full"
                >
                  {generating || trainingStatus === 'generating' ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Training with {selectedProvider === 'openai' ? 'OpenAI' :
                                   selectedProvider === 'gemini' ? 'Gemini' :
                                   selectedProvider === 'mock' ? 'Mock AI' : selectedProvider}...
                    </>
                  ) : (
                    <>
                      <Zap className="mr-2 h-4 w-4" />
                      Train Bot with AI
                    </>
                  )}
                </Button>

                {(generating || trainingStatus === 'generating') && (
                  <div className="text-xs text-center text-muted-foreground">
                    Using {selectedProvider === 'openai' ? '🤖 OpenAI GPT-3.5' :
                           selectedProvider === 'gemini' ? '🧠 Google Gemini' :
                           selectedProvider === 'mock' ? '🎭 Mock AI' : selectedProvider} to analyze your business
                  </div>
                )}
              </div>
            </div>
          )}

          {!readiness.ready && (
            <div className="p-3 bg-yellow-50 rounded-lg">
              <p className="text-sm text-yellow-800">
                <strong>Action Required:</strong> Please complete the business information section 
                before training your bot with AI.
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Training Progress */}
      {trainingStatus === 'generating' && (
        <Card>
          <CardContent className="pt-6">
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Training Progress</span>
                <span className="text-sm text-muted-foreground">{progress}%</span>
              </div>
              <Progress value={progress} className="h-2" />
              <p className="text-sm text-muted-foreground">
                Generating AI responses based on your business information...
              </p>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Coverage Stats */}
      {botCoverage && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span>Training Results</span>
              {responseSchemas.length > 0 && responseSchemas[0].provider_used && (
                <Badge variant="secondary" className="text-xs">
                  {responseSchemas[0].provider_used === 'openai' ? '🤖 OpenAI' :
                   responseSchemas[0].provider_used === 'gemini' ? '🧠 Gemini' :
                   responseSchemas[0].provider_used === 'mock' ? '🎭 Mock' :
                   responseSchemas[0].provider_used}
                </Badge>
              )}
            </CardTitle>
            <CardDescription>
              AI-generated responses for your business
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Response Coverage</span>
                <span className="text-sm text-muted-foreground">
                  {botCoverage.answered_questions} / {botCoverage.total_questions} questions
                </span>
              </div>
              <Progress value={botCoverage.coverage_percentage} className="h-2" />
              <p className="text-sm text-muted-foreground">
                {botCoverage.coverage_percentage}% of expected customer questions have AI responses
              </p>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Generated Responses */}
      {responseSchemas.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span>Generated Responses</span>
              <div className="flex items-center gap-2">
                {responseSchemas.length > 0 && responseSchemas[0].provider_used && (
                  <Badge variant="outline" className="text-xs">
                    Generated by {responseSchemas[0].provider_used === 'openai' ? '🤖 OpenAI' :
                                 responseSchemas[0].provider_used === 'gemini' ? '🧠 Gemini' :
                                 responseSchemas[0].provider_used === 'mock' ? '🎭 Mock' :
                                 responseSchemas[0].provider_used}
                  </Badge>
                )}
                <Button variant="outline" size="sm" onClick={refetch}>
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Refresh
                </Button>
              </div>
            </CardTitle>
            <CardDescription>
              Review and customize AI-generated responses for your business
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {responseSchemas.slice(0, 5).map((schema) => (
                <div key={schema.id} className="border rounded-lg p-4">
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex-1">
                      <h4 className="font-medium text-sm">
                        {schema.custom_question || schema.question_template?.question_text}
                      </h4>
                      <div className="flex items-center gap-2 mt-1">
                        <Badge variant="outline" className="text-xs">
                          {schema.custom_intent || schema.question_template?.question_intent}
                        </Badge>
                        <Badge variant="outline" className="text-xs">
                          {schema.provider_used}
                        </Badge>
                        {schema.custom_question && (
                          <Badge variant="secondary" className="text-xs">
                            Business-Specific
                          </Badge>
                        )}
                        {schema.is_approved ? (
                          <Badge className="text-xs bg-green-100 text-green-800">
                            <CheckCircle className="h-3 w-3 mr-1" />
                            Approved
                          </Badge>
                        ) : (
                          <Badge variant="secondary" className="text-xs">
                            Pending Review
                          </Badge>
                        )}
                      </div>
                    </div>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleEditResponse(schema.id, schema.custom_response || schema.ai_generated_response)}
                    >
                      <Edit3 className="h-4 w-4" />
                    </Button>
                  </div>

                  {editingResponse === schema.id ? (
                    <div className="space-y-3">
                      <Textarea
                        value={editText}
                        onChange={(e) => setEditText(e.target.value)}
                        rows={4}
                        placeholder="Edit the response..."
                      />
                      <div className="flex gap-2">
                        <Button size="sm" onClick={() => handleSaveEdit(schema.id)}>
                          <Save className="h-4 w-4 mr-2" />
                          Save
                        </Button>
                        <Button 
                          size="sm" 
                          variant="outline" 
                          onClick={() => setEditingResponse(null)}
                        >
                          Cancel
                        </Button>
                      </div>
                    </div>
                  ) : (
                    <div className="space-y-3">
                      <div className="bg-gray-50 p-3 rounded text-sm">
                        {schema.custom_response || schema.ai_generated_response}
                      </div>

                      {/* Display CTA buttons if available */}
                      {schema.cta_buttons && schema.cta_type !== 'none' && (
                        <div className="bg-blue-50 p-3 rounded">
                          <div className="text-xs font-medium text-blue-800 mb-2">
                            📱 Call-to-Action Buttons:
                          </div>
                          <div className="flex flex-wrap gap-2">
                            {(() => {
                              try {
                                const buttons = typeof schema.cta_buttons === 'string'
                                  ? JSON.parse(schema.cta_buttons)
                                  : schema.cta_buttons;
                                return buttons.map((button: any, index: number) => (
                                  <div
                                    key={index}
                                    className="inline-flex items-center gap-1 px-2 py-1 bg-white border rounded text-xs"
                                  >
                                    {button.type === 'url' && '🔗'}
                                    {button.type === 'phone' && '📞'}
                                    {button.type === 'quick_reply' && '💬'}
                                    {button.text}
                                  </div>
                                ));
                              } catch (error) {
                                return <span className="text-xs text-red-500">Error parsing CTA buttons</span>;
                              }
                            })()}
                          </div>
                        </div>
                      )}
                      {!schema.is_approved && (
                        <Button 
                          size="sm" 
                          onClick={() => handleApproveResponse(schema.id)}
                        >
                          <CheckCircle className="h-4 w-4 mr-2" />
                          Approve
                        </Button>
                      )}
                    </div>
                  )}
                </div>
              ))}

              {responseSchemas.length > 5 && (
                <p className="text-sm text-muted-foreground text-center">
                  And {responseSchemas.length - 5} more responses...
                </p>
              )}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};
