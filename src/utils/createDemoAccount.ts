import { supabase } from '@/integrations/supabase/client';

/**
 * Utility function to create a demo account for testing
 * This should only be used in development
 */
export const createDemoAccount = async () => {
  try {
    console.log('Creating demo account...');
    
    // First, try to sign up the demo user
    const { data, error } = await supabase.auth.signUp({
      email: '<EMAIL>',
      password: 'demo123',
      options: {
        data: {
          first_name: 'Demo',
          last_name: 'User',
          full_name: 'Demo User'
        }
      }
    });

    if (error) {
      console.error('Error creating demo account:', error);
      return { success: false, error: error.message };
    }

    console.log('Demo account created successfully:', data);
    return { success: true, data };
    
  } catch (error) {
    console.error('Unexpected error:', error);
    return { success: false, error: 'Unexpected error occurred' };
  }
};

/**
 * Test login with demo credentials
 */
export const testDemoLogin = async () => {
  try {
    console.log('Testing demo login...');
    
    const { data, error } = await supabase.auth.signInWithPassword({
      email: '<EMAIL>',
      password: 'demo123'
    });

    if (error) {
      console.error('Demo login failed:', error);
      return { success: false, error: error.message };
    }

    console.log('Demo login successful:', data);
    return { success: true, data };
    
  } catch (error) {
    console.error('Unexpected error during login test:', error);
    return { success: false, error: 'Unexpected error occurred' };
  }
};
