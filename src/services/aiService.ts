// AI Service for handling different AI providers
import { GoogleGenerativeAI } from '@google/generative-ai';

export interface AIProvider {
  name: string;
  generateResponse: (prompt: string, context?: AIContext) => Promise<string>;
}

export interface AIContext {
  botName?: string;
  botDescription?: string;
  tone?: string;
  language?: string;
  conversationHistory?: Array<{ role: 'user' | 'assistant'; content: string }>;
  systemPrompt?: string;
}

export interface AIResponse {
  content: string;
  provider: string;
  tokensUsed?: number;
  processingTime?: number;
}

class OpenAIProvider implements AIProvider {
  name = 'OpenAI GPT-3.5';
  private apiKey: string;
  private baseUrl = 'https://api.openai.com/v1';

  constructor(apiKey: string) {
    this.apiKey = apiKey;
  }

  async generateResponse(prompt: string, context?: AIContext): Promise<string> {
    try {
      const systemPrompt = this.buildSystemPrompt(context);
      const messages = [
        { role: 'system', content: systemPrompt },
        ...(context?.conversationHistory || []),
        { role: 'user', content: prompt }
      ];

      const response = await fetch(`${this.baseUrl}/chat/completions`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          model: 'gpt-3.5-turbo',
          messages: messages,
          max_tokens: 500,
          temperature: 0.7,
        }),
      });

      if (!response.ok) {
        throw new Error(`OpenAI API error: ${response.status}`);
      }

      const data = await response.json();
      return data.choices[0]?.message?.content || 'I apologize, but I could not generate a response.';
    } catch (error) {
      console.error('OpenAI API error:', error);
      throw new Error('Failed to generate AI response');
    }
  }

  private buildSystemPrompt(context?: AIContext): string {
    let prompt = `You are ${context?.botName || 'an AI assistant'}.`;
    
    if (context?.botDescription) {
      prompt += ` ${context.botDescription}`;
    }
    
    if (context?.tone) {
      prompt += ` Your tone should be ${context.tone}.`;
    }
    
    if (context?.language && context.language !== 'English') {
      prompt += ` Please respond in ${context.language}.`;
    }
    
    prompt += ' Be helpful, concise, and professional. If you cannot help with something, politely explain why.';
    
    if (context?.systemPrompt) {
      prompt += ` Additional instructions: ${context.systemPrompt}`;
    }
    
    return prompt;
  }
}

class GeminiProvider implements AIProvider {
  name = 'Google Gemini 1.5 Flash';
  private genAI: GoogleGenerativeAI;

  constructor(private apiKey: string) {
    this.genAI = new GoogleGenerativeAI(apiKey);
  }

  async generateResponse(prompt: string, context?: AIContext): Promise<string> {
    try {
      const systemPrompt = this.buildSystemPrompt(context);

      // Try different model names in order of preference
      const modelNames = ["gemini-1.5-flash", "gemini-1.5-pro", "gemini-pro"];
      let model: any = null;
      let lastError: any = null;

      for (const modelName of modelNames) {
        try {
          console.log(`🔄 Trying Gemini model: ${modelName}`);
          model = this.genAI.getGenerativeModel({ model: modelName });
          this.name = `Google ${modelName}`;
          console.log(`✅ Using model: ${modelName}`);
          break;
        } catch (error) {
          console.warn(`❌ Model ${modelName} failed:`, error);
          lastError = error;
          model = null;
          continue;
        }
      }

      if (!model) {
        throw new Error(`All Gemini models failed. Last error: ${lastError}`);
      }

      // Build conversation history for Gemini
      const history = [];
      if (context?.conversationHistory && context.conversationHistory.length > 0) {
        for (const msg of context.conversationHistory) {
          history.push({
            role: msg.role === 'user' ? 'user' : 'model',
            parts: [{ text: msg.content }]
          });
        }
      }

      // Create chat session with history
      const chat = model.startChat({
        history: history,
        generationConfig: {
          temperature: 0.7,
          topK: 40,
          topP: 0.95,
          maxOutputTokens: 500,
        },
      });

      // Combine system prompt with user prompt
      const fullPrompt = `${systemPrompt}\n\nUser: ${prompt}`;

      const result = await chat.sendMessage(fullPrompt);
      const response = await result.response;
      const text = response.text();

      if (!text) {
        throw new Error('No response generated from Gemini');
      }

      return text.trim();
    } catch (error) {
      console.error('Gemini API error:', error);
      throw new Error(`Failed to generate Gemini response: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private buildSystemPrompt(context?: AIContext): string {
    let prompt = `You are ${context?.botName || 'an AI assistant'}.`;

    if (context?.botDescription) {
      prompt += ` ${context.botDescription}`;
    }

    if (context?.tone) {
      prompt += ` Your tone should be ${context.tone}.`;
    }

    if (context?.language && context.language !== 'English') {
      prompt += ` Please respond in ${context.language}.`;
    }

    prompt += ' Be helpful, concise, and professional. If you cannot help with something, politely explain why.';

    if (context?.systemPrompt) {
      prompt += ` Additional instructions: ${context.systemPrompt}`;
    }

    return prompt;
  }
}

// Mock AI Provider for testing without API keys
class MockAIProvider implements AIProvider {
  name = 'Mock AI';

  async generateResponse(prompt: string, context?: AIContext): Promise<string> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000));
    
    const responses = [
      `Thank you for your message: "${prompt}". How can I help you further?`,
      `I understand you're asking about "${prompt}". Let me provide some assistance.`,
      `That's an interesting question about "${prompt}". Here's what I think...`,
      `I'd be happy to help with "${prompt}". Let me give you some information.`,
      `Regarding "${prompt}", I can certainly assist you with that.`
    ];
    
    const randomResponse = responses[Math.floor(Math.random() * responses.length)];
    
    // Add context-aware elements
    let response = randomResponse;
    if (context?.tone === 'friendly') {
      response = `Hi there! 😊 ${response}`;
    } else if (context?.tone === 'professional') {
      response = `Good day. ${response}`;
    } else if (context?.tone === 'casual') {
      response = `Hey! ${response}`;
    }
    
    if (context?.botName) {
      response += ` - ${context.botName}`;
    }
    
    return response;
  }
}

// AI Service Manager
class AIService {
  private providers: Map<string, AIProvider> = new Map();
  private defaultProvider = 'mock';

  constructor() {
    // Initialize with mock provider
    this.providers.set('mock', new MockAIProvider());

    // Initialize OpenAI if API key is available
    const openaiKey = import.meta.env.VITE_OPENAI_API_KEY;
    if (openaiKey) {
      this.providers.set('openai', new OpenAIProvider(openaiKey));
      this.defaultProvider = 'openai';
    }

    // Initialize Gemini if API key is available
    const geminiKey = import.meta.env.VITE_GEMINI_API_KEY;
    if (geminiKey) {
      this.providers.set('gemini', new GeminiProvider(geminiKey));
      // If no OpenAI, make Gemini the default
      if (!openaiKey) {
        this.defaultProvider = 'gemini';
      }
    }
  }

  async generateResponse(
    prompt: string,
    context?: AIContext,
    providerName?: string
  ): Promise<AIResponse> {
    const startTime = Date.now();
    const provider = this.providers.get(providerName || this.defaultProvider);
    
    if (!provider) {
      throw new Error(`AI provider '${providerName || this.defaultProvider}' not found`);
    }

    try {
      console.log(`🤖 Generating AI response using ${provider.name}...`);
      console.log('📝 Prompt:', prompt);
      console.log('🎯 Context:', context);
      
      const content = await provider.generateResponse(prompt, context);
      const processingTime = Date.now() - startTime;
      
      console.log(`✅ AI response generated in ${processingTime}ms`);
      console.log('💬 Response:', content);
      
      return {
        content,
        provider: provider.name,
        processingTime,
        tokensUsed: Math.floor(prompt.length / 4) + Math.floor(content.length / 4) // Rough estimate
      };
    } catch (error) {
      console.error(`❌ AI generation failed with ${provider.name}:`, error);
      throw error;
    }
  }

  getAvailableProviders(): string[] {
    return Array.from(this.providers.keys());
  }

  setDefaultProvider(providerName: string): void {
    if (this.providers.has(providerName)) {
      this.defaultProvider = providerName;
    } else {
      throw new Error(`Provider '${providerName}' not available`);
    }
  }

  async generateBusinessQuestionsAndResponses(
    businessInfo: any,
    documents: any[] = [],
    providerName?: string
  ): Promise<Array<{ question: string; response: string; intent: string; cta?: any }>> {
    const provider = this.providers.get(providerName || this.defaultProvider);

    if (!provider) {
      throw new Error(`AI provider '${providerName || this.defaultProvider}' not found`);
    }

    try {
      console.log('🤖 Generating business-specific questions and responses...');

      // Build comprehensive business context
      let businessContext = `Business Information:\n`;
      businessContext += `Name: ${businessInfo.business_name}\n`;
      businessContext += `Type: ${businessInfo.business_type}\n`;
      businessContext += `Description: ${businessInfo.description}\n`;

      if (businessInfo.address) businessContext += `Address: ${businessInfo.address}\n`;
      if (businessInfo.phone) businessContext += `Phone: ${businessInfo.phone}\n`;
      if (businessInfo.email) businessContext += `Email: ${businessInfo.email}\n`;
      if (businessInfo.website) businessContext += `Website: ${businessInfo.website}\n`;

      // Add business hours
      businessContext += `\nBusiness Hours:\n`;
      Object.entries(businessInfo.hours || {}).forEach(([day, hours]) => {
        businessContext += `${day}: ${hours}\n`;
      });

      // Add services/products
      if (businessInfo.services && businessInfo.services.length > 0) {
        businessContext += `\nServices/Products: ${businessInfo.services.join(', ')}\n`;
      }

      // Add special information
      if (businessInfo.special_info) {
        businessContext += `\nSpecial Information: ${businessInfo.special_info}\n`;
      }

      // Add document context
      if (documents && documents.length > 0) {
        businessContext += `\nDocument Information:\n`;
        documents.forEach((doc, index) => {
          if (doc.content) {
            businessContext += `Document ${index + 1} (${doc.file_name || 'Unknown'}): ${doc.content.substring(0, 1000)}...\n`;
          }
        });
      }

      // Generate questions and responses using AI
      const prompt = `Based on the following business information, generate 10-15 common customer questions and appropriate responses for this specific business.

${businessContext}

Please provide the output in the following JSON format with Call-to-Action (CTA) buttons where appropriate:
[
  {
    "question": "What are your opening hours?",
    "response": "We are open Monday-Friday 9AM-9PM, Saturday-Sunday 10AM-8PM.",
    "intent": "hours_inquiry",
    "cta": {
      "type": "buttons",
      "buttons": [
        {
          "text": "📍 Get Directions",
          "type": "url",
          "value": "https://maps.google.com/search/[business_address]",
          "whatsapp_type": "url"
        },
        {
          "text": "📞 Call Us",
          "type": "phone",
          "value": "[business_phone]",
          "whatsapp_type": "phone_number"
        }
      ]
    }
  },
  {
    "question": "What services do you offer?",
    "response": "We offer [specific services based on the business info]",
    "intent": "services_inquiry",
    "cta": {
      "type": "buttons",
      "buttons": [
        {
          "text": "📅 Book Now",
          "type": "url",
          "value": "[booking_url_or_website]",
          "whatsapp_type": "url"
        },
        {
          "text": "💬 More Info",
          "type": "quick_reply",
          "value": "more_info"
        }
      ]
    }
  }
]

IMPORTANT CTA Guidelines:
- Include CTA buttons for questions where customers might want to take action (booking, calling, visiting website, getting directions)
- Use actual business information for URLs and phone numbers from the provided data
- Maximum 3 buttons per response (WhatsApp limitation)
- Button text should be 20 characters or less
- Common CTA types: "Book Now", "Call Us", "Get Directions", "Visit Website", "View Menu", "More Info"
- Use appropriate emojis in button text
- For restaurants: include "Book Table", "View Menu", "Call Restaurant"
- For retail: include "Shop Online", "Get Directions", "Contact Us"
- For services: include "Book Appointment", "Our Services", "Call Now"

Make sure the responses are specific to this business using the exact information provided (hours, services, contact details, etc.). The questions should be realistic for customers of this type of business.`;

      const aiContext: AIContext = {
        botName: businessInfo.business_name,
        tone: 'professional',
        language: 'English',
        systemPrompt: 'You are an expert at creating customer service Q&A for businesses. Generate realistic questions customers would ask and accurate responses based on the business information provided.'
      };

      const response = await provider.generateResponse(prompt, aiContext);

      // Try to parse JSON response
      try {
        const cleanResponse = response.replace(/```json\n?|\n?```/g, '').trim();
        const questionsAndResponses = JSON.parse(cleanResponse);

        if (Array.isArray(questionsAndResponses)) {
          console.log(`✅ Generated ${questionsAndResponses.length} business-specific Q&As`);
          return questionsAndResponses;
        } else {
          throw new Error('Response is not an array');
        }
      } catch (parseError) {
        console.error('Failed to parse AI response as JSON:', parseError);
        console.log('Raw response:', response);

        // Fallback: create basic questions based on business info
        return this.generateFallbackQuestions(businessInfo);
      }

    } catch (error) {
      console.error('❌ Error generating business Q&As:', error);
      throw error;
    }
  }

  private generateFallbackQuestions(businessInfo: any): Array<{ question: string; response: string; intent: string }> {
    const questions = [];

    // Basic questions every business should answer
    questions.push({
      question: "What are your opening hours?",
      response: Object.entries(businessInfo.hours || {})
        .map(([day, hours]) => `${day.charAt(0).toUpperCase() + day.slice(1)}: ${hours}`)
        .join('\n'),
      intent: "hours_inquiry"
    });

    if (businessInfo.address) {
      questions.push({
        question: "Where are you located?",
        response: `We are located at ${businessInfo.address}`,
        intent: "location_inquiry"
      });
    }

    if (businessInfo.phone) {
      questions.push({
        question: "What is your phone number?",
        response: `You can reach us at ${businessInfo.phone}`,
        intent: "contact_inquiry"
      });
    }

    if (businessInfo.services && businessInfo.services.length > 0) {
      questions.push({
        question: "What services do you offer?",
        response: `We offer the following services: ${businessInfo.services.join(', ')}`,
        intent: "services_inquiry"
      });
    }

    if (businessInfo.description) {
      questions.push({
        question: "Tell me about your business",
        response: businessInfo.description,
        intent: "business_info"
      });
    }

    return questions;
  }
}

// Export singleton instance
export const aiService = new AIService();
export default aiService;
