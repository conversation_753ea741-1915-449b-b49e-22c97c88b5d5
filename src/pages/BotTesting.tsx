
import { useState, useEffect } from "react";
import { useParams } from "react-router-dom";
import { Send, Smartphone, MessageCircle, MessageSquare, ExternalLink, Phone } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { useLanguage } from "@/contexts/LanguageContext";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";

interface BotResponseSchema {
  id: string;
  custom_question: string | null;
  ai_generated_response: string;
  custom_response: string | null;
  custom_intent: string | null;
  cta_buttons: any | null;
  cta_type: string | null;
  is_approved: boolean;
  is_active: boolean;
  question_template?: {
    question_text: string;
    question_intent: string;
  };
}

interface ChatMessage {
  id: number;
  text: string;
  sender: 'user' | 'bot';
  timestamp: Date;
  cta_buttons?: any[];
}

const BotTesting = () => {
  const { botId } = useParams<{ botId?: string }>();
  const { t, direction } = useLanguage();
  const { toast } = useToast();
  const [messages, setMessages] = useState<ChatMessage[]>([
    {
      id: 1,
      text: t('botGreeting'),
      sender: "bot",
      timestamp: new Date(Date.now() - 60000)
    }
  ]);
  const [inputMessage, setInputMessage] = useState("");
  const [selectedChannel, setSelectedChannel] = useState("whatsapp");
  const [responseSchemas, setResponseSchemas] = useState<BotResponseSchema[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  const channels = [
    { id: "whatsapp", name: "WhatsApp", icon: MessageCircle, color: "bg-green-500" },
    { id: "facebook", name: "Facebook", icon: MessageSquare, color: "bg-blue-500" },
    { id: "sms", name: "SMS", icon: Smartphone, color: "bg-purple-500" }
  ];

  // Load trained response schemas when component mounts
  useEffect(() => {
    const loadResponseSchemas = async () => {
      if (!botId) return;

      try {
        setIsLoading(true);
        console.log('🤖 Loading trained responses for bot:', botId);

        const { data, error } = await supabase
          .from('bot_response_schemas')
          .select(`
            id,
            custom_question,
            ai_generated_response,
            custom_response,
            custom_intent,
            cta_buttons,
            cta_type,
            is_approved,
            is_active,
            question_template:question_templates(
              question_text,
              question_intent
            )
          `)
          .eq('bot_id', botId)
          .eq('is_active', true);

        if (error) {
          console.error('Error loading response schemas:', error);
          toast({
            title: "Warning",
            description: "Could not load trained responses. Using fallback responses.",
            variant: "destructive",
          });
          return;
        }

        console.log('✅ Loaded response schemas:', data);
        setResponseSchemas(data || []);

        if (data && data.length > 0) {
          toast({
            title: "Bot Ready!",
            description: `Loaded ${data.length} trained responses for this bot.`,
          });
        } else {
          toast({
            title: "No Training Data",
            description: "This bot hasn't been trained yet. Please train it first in the Training tab.",
            variant: "destructive",
          });
        }

      } catch (error) {
        console.error('Error loading response schemas:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadResponseSchemas();
  }, [botId, toast]);

  const sendMessage = () => {
    if (!inputMessage.trim()) return;

    const userMessage = {
      id: messages.length + 1,
      text: inputMessage,
      sender: "user" as const,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputMessage("");

    // Simulate bot response
    setTimeout(() => {
      const response = getBotResponse(inputMessage);
      const botResponse: ChatMessage = {
        id: messages.length + 2,
        text: response.text,
        sender: "bot" as const,
        timestamp: new Date(),
        cta_buttons: response.cta_buttons
      };
      setMessages(prev => [...prev, botResponse]);
    }, 1000);
  };

  const getBotResponse = (userMessage: string): { text: string; cta_buttons?: any[] } => {
    const message = userMessage.toLowerCase().trim();

    console.log('🔍 Looking for response to:', message);
    console.log('📚 Available schemas:', responseSchemas.length);

    // If no trained responses available, use fallback
    if (responseSchemas.length === 0) {
      return {
        text: "I'm still learning! Please train me first by going to the Training tab and adding your business information."
      };
    }

    // Try to find a matching response from trained data
    let bestMatch: BotResponseSchema | null = null;
    let bestScore = 0;

    for (const schema of responseSchemas) {
      const question = (schema.custom_question || schema.question_template?.question_text || '').toLowerCase();
      const intent = (schema.custom_intent || schema.question_template?.question_intent || '').toLowerCase();

      // Calculate similarity score
      let score = 0;

      // Check for exact word matches in question
      const questionWords = question.split(' ').filter(word => word.length > 2);
      const messageWords = message.split(' ').filter(word => word.length > 2);

      for (const qWord of questionWords) {
        if (messageWords.some(mWord => mWord.includes(qWord) || qWord.includes(mWord))) {
          score += 2;
        }
      }

      // Check for intent-based matches
      if (intent) {
        if (intent.includes('hours') && (message.includes('hours') || message.includes('open') || message.includes('close'))) {
          score += 3;
        }
        if (intent.includes('menu') && (message.includes('menu') || message.includes('food') || message.includes('eat'))) {
          score += 3;
        }
        if (intent.includes('location') && (message.includes('where') || message.includes('address') || message.includes('location'))) {
          score += 3;
        }
        if (intent.includes('contact') && (message.includes('phone') || message.includes('call') || message.includes('contact'))) {
          score += 3;
        }
        if (intent.includes('services') && (message.includes('services') || message.includes('offer') || message.includes('do'))) {
          score += 3;
        }
        if (intent.includes('reservation') && (message.includes('book') || message.includes('reservation') || message.includes('table'))) {
          score += 3;
        }
      }

      // Update best match if this score is higher
      if (score > bestScore) {
        bestScore = score;
        bestMatch = schema;
      }
    }

    console.log('🎯 Best match score:', bestScore, 'Schema:', bestMatch?.custom_question || bestMatch?.question_template?.question_text);

    // If we found a good match (score > 1), use it
    if (bestMatch && bestScore > 1) {
      const response = bestMatch.custom_response || bestMatch.ai_generated_response;
      console.log('✅ Using trained response:', response);

      // Parse CTA buttons if available
      let ctaButtons = null;
      if (bestMatch.cta_buttons) {
        try {
          ctaButtons = typeof bestMatch.cta_buttons === 'string'
            ? JSON.parse(bestMatch.cta_buttons)
            : bestMatch.cta_buttons;
        } catch (error) {
          console.error('Error parsing CTA buttons:', error);
        }
      }

      return {
        text: response,
        cta_buttons: ctaButtons
      };
    }

    // Fallback: try to find any response that might be relevant
    if (responseSchemas.length > 0) {
      // Look for a general business info response
      const generalResponse = responseSchemas.find(schema =>
        (schema.custom_intent || schema.question_template?.question_intent || '').toLowerCase().includes('business')
      );

      if (generalResponse) {
        return {
          text: generalResponse.custom_response || generalResponse.ai_generated_response
        };
      }

      // If no specific match, provide a helpful response listing what we can help with
      const availableTopics = responseSchemas
        .map(schema => schema.custom_intent || schema.question_template?.question_intent)
        .filter(Boolean)
        .slice(0, 5)
        .join(', ');

      return {
        text: `I can help you with: ${availableTopics}. What would you like to know?`
      };
    }

    // Final fallback
    return {
      text: "I'm still learning about this business. Please ask me about our hours, location, services, or contact information."
    };
  };

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  const handleCTAClick = (button: any) => {
    if (button.type === 'url') {
      window.open(button.value, '_blank');
    } else if (button.type === 'phone') {
      window.open(`tel:${button.value}`);
    } else if (button.type === 'quick_reply') {
      // Simulate user clicking a quick reply
      setInputMessage(button.text);
      setTimeout(() => {
        const userMessage: ChatMessage = {
          id: messages.length + 1,
          text: button.text,
          sender: "user",
          timestamp: new Date()
        };
        setMessages(prev => [...prev, userMessage]);

        // Get bot response
        setTimeout(() => {
          const response = getBotResponse(button.text);
          const botResponse: ChatMessage = {
            id: messages.length + 2,
            text: response.text,
            sender: "bot",
            timestamp: new Date(),
            cta_buttons: response.cta_buttons
          };
          setMessages(prev => [...prev, botResponse]);
        }, 1000);
      }, 100);
    }
  };

  const CTAButtons = ({ buttons }: { buttons: any[] }) => {
    if (!buttons || buttons.length === 0) return null;

    return (
      <div className="flex flex-wrap gap-2 mt-2">
        {buttons.slice(0, 3).map((button, index) => (
          <Button
            key={index}
            variant="outline"
            size="sm"
            onClick={() => handleCTAClick(button)}
            className="text-xs"
          >
            {button.type === 'url' && <ExternalLink className="w-3 h-3 mr-1" />}
            {button.type === 'phone' && <Phone className="w-3 h-3 mr-1" />}
            {button.text}
          </Button>
        ))}
      </div>
    );
  };

  return (
    <div className="space-y-6" dir={direction}>
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold text-gray-900">{t('testYourChatbot')}</h1>
        <p className="text-gray-600 mt-1">{t('simulateConversations')}</p>
      </div>

      <div className="grid lg:grid-cols-3 gap-6">
        {/* Channel Selection */}
        <div className="lg:col-span-1">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">{t('selectChannel')}</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              {channels.map((channel) => (
                <button
                  key={channel.id}
                  onClick={() => setSelectedChannel(channel.id)}
                  className={`w-full flex items-center gap-3 p-3 rounded-lg border-2 transition-all ${
                    selectedChannel === channel.id
                      ? "border-teal-500 bg-teal-50"
                      : "border-gray-200 hover:border-gray-300"
                  }`}
                >
                  <div className={`w-10 h-10 ${channel.color} rounded-lg flex items-center justify-center`}>
                    <channel.icon className="w-5 h-5 text-white" />
                  </div>
                  <div className="text-left">
                    <p className="font-medium text-gray-900">{channel.name}</p>
                    <p className="text-sm text-gray-500">Test on {channel.name}</p>
                  </div>
                </button>
              ))}
            </CardContent>
          </Card>

          {/* Bot Info */}
          <Card className="mt-6">
            <CardHeader>
              <CardTitle className="text-lg">Current Bot</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center gap-3 mb-4">
                <div className="w-12 h-12 bg-gradient-to-br from-teal-500 to-blue-600 rounded-lg flex items-center justify-center">
                  <MessageCircle className="w-6 h-6 text-white" />
                </div>
                <div>
                  <p className="font-semibold text-gray-900">Restaurant Assistant</p>
                  <Badge className="bg-green-100 text-green-800">Active</Badge>
                </div>
              </div>
              <div className="space-y-2 text-sm text-gray-600">
                <p><span className="font-medium">Files:</span> menu.pdf, hours.txt</p>
                <p><span className="font-medium">Last Updated:</span> 2 hours ago</p>
                <p><span className="font-medium">Confidence:</span> 95%</p>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Chat Interface */}
        <div className="lg:col-span-2">
          <Card className="h-[600px] flex flex-col">
            <CardHeader className="pb-4">
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center gap-2">
                  <MessageCircle className="w-5 h-5" />
                  Chat Test - {channels.find(c => c.id === selectedChannel)?.name}
                </CardTitle>
                <Button 
                  variant="outline" 
                  size="sm"
                  onClick={() => setMessages([{
                    id: 1,
                    text: "Hello! I'm your Restaurant Assistant. How can I help you today?",
                    sender: "bot",
                    timestamp: new Date()
                  }])}
                >
                  Clear Chat
                </Button>
              </div>
            </CardHeader>

            <CardContent className="flex-1 flex flex-col">
              {/* Messages */}
              <div className="flex-1 overflow-y-auto space-y-4 mb-4 pr-2">
                {messages.map((message) => (
                  <div
                    key={message.id}
                    className={`flex ${message.sender === 'user' ? 'justify-end' : 'justify-start'}`}
                  >
                    <div
                      className={`max-w-xs lg:max-w-md px-4 py-2 rounded-2xl ${
                        message.sender === 'user'
                          ? 'bg-teal-500 text-white'
                          : 'bg-gray-100 text-gray-900'
                      }`}
                    >
                      <p className="text-sm">{message.text}</p>
                      <p className={`text-xs mt-1 ${
                        message.sender === 'user' ? 'text-teal-100' : 'text-gray-500'
                      }`}>
                        {formatTime(message.timestamp)}
                      </p>
                      {message.sender === 'bot' && message.cta_buttons && (
                        <CTAButtons buttons={message.cta_buttons} />
                      )}
                    </div>
                  </div>
                ))}
              </div>

              {/* Input */}
              <div className="flex gap-2">
                <Input
                  value={inputMessage}
                  onChange={(e) => setInputMessage(e.target.value)}
                  placeholder="Type your message..."
                  onKeyDown={(e) => e.key === 'Enter' && sendMessage()}
                  className="flex-1"
                />
                <Button 
                  onClick={sendMessage}
                  disabled={!inputMessage.trim()}
                  className="bg-teal-500 hover:bg-teal-600"
                >
                  <Send className="w-4 h-4" />
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default BotTesting;
