import React, { useState } from 'react';
import { usePara<PERSON>, useNavigate } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useAuth } from '@/contexts/AuthContext';
import { useBots } from '@/hooks/useBots';
import { useBusinessInfo } from '@/hooks/useBusinessInfo';
import { BusinessInfoForm } from '@/components/BusinessInfoForm';
import { AITrainingManager } from '@/components/AITrainingManager';
import { BusinessTrainingDemo } from '@/components/BusinessTrainingDemo';
import { useToast } from '@/hooks/use-toast';
import { 
  ArrowLeft, 
  Building2, 
  Brain, 
  FileText, 
  MessageSquare,
  Target,
  Loader2
} from 'lucide-react';

const BotTraining = () => {
  const { botId } = useParams<{ botId: string }>();
  const navigate = useNavigate();
  const { user } = useAuth();
  const { toast } = useToast();
  
  const { getBotById } = useBots();
  const { businessInfo, saveBusinessInfo, loading: businessLoading } = useBusinessInfo(botId);
  
  const [bot, setBot] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  React.useEffect(() => {
    loadBotData();
  }, [botId]);

  const loadBotData = async () => {
    if (!botId) return;
    
    try {
      setLoading(true);
      const botData = await getBotById(botId);
      if (botData) {
        setBot(botData);
      } else {
        toast({
          title: "Error",
          description: "Bot not found",
          variant: "destructive",
        });
        navigate('/dashboard');
      }
    } catch (error) {
      console.error('Error loading bot:', error);
      toast({
        title: "Error",
        description: "Failed to load bot data",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleBusinessInfoSave = async (info: any) => {
    await saveBusinessInfo(info);
    toast({
      title: "Success",
      description: "Business information saved successfully",
    });
  };

  const handleTrainingComplete = () => {
    toast({
      title: "Training Complete",
      description: "Your bot has been trained with AI responses",
    });
  };

  if (!user) {
    return (
      <div className="container mx-auto py-8">
        <Card>
          <CardContent className="pt-6">
            <p className="text-center text-muted-foreground">
              Please log in to access bot training
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="container mx-auto py-8">
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-center">
              <Loader2 className="h-6 w-6 animate-spin mr-2" />
              <span>Loading bot data...</span>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!bot) {
    return (
      <div className="container mx-auto py-8">
        <Card>
          <CardContent className="pt-6">
            <p className="text-center text-muted-foreground">
              Bot not found
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            size="sm"
            onClick={() => navigate('/dashboard')}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Dashboard
          </Button>
          <div>
            <h1 className="text-2xl font-bold">Train Your Bot</h1>
            <p className="text-muted-foreground">
              Configure business information and train {bot.name} with AI
            </p>
          </div>
        </div>
        <Badge variant="outline" className="flex items-center gap-2">
          <MessageSquare className="h-4 w-4" />
          {bot.channel}
        </Badge>
      </div>

      {/* Bot Info Card */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Target className="h-5 w-5" />
            {bot.name}
          </CardTitle>
          <CardDescription>
            Status: <Badge variant={bot.status === 'active' ? 'default' : 'secondary'}>
              {bot.status}
            </Badge>
          </CardDescription>
        </CardHeader>
      </Card>

      {/* Main Content Tabs */}
      <Tabs defaultValue="demo" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="demo" className="flex items-center gap-2">
            <Target className="h-4 w-4" />
            Demo
          </TabsTrigger>
          <TabsTrigger value="business" className="flex items-center gap-2">
            <Building2 className="h-4 w-4" />
            Business Info
          </TabsTrigger>
          <TabsTrigger value="training" className="flex items-center gap-2">
            <Brain className="h-4 w-4" />
            AI Training
          </TabsTrigger>
          <TabsTrigger value="documents" className="flex items-center gap-2">
            <FileText className="h-4 w-4" />
            Documents
          </TabsTrigger>
        </TabsList>

        <TabsContent value="demo" className="space-y-4">
          <BusinessTrainingDemo />
        </TabsContent>

        <TabsContent value="business" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Business Information</CardTitle>
              <CardDescription>
                Provide details about your business to help AI generate better responses
              </CardDescription>
            </CardHeader>
            <CardContent>
              {businessLoading ? (
                <div className="flex items-center justify-center py-8">
                  <Loader2 className="h-6 w-6 animate-spin mr-2" />
                  <span>Loading business information...</span>
                </div>
              ) : (
                <BusinessInfoForm
                  botId={botId!}
                  initialData={businessInfo || undefined}
                  onSave={handleBusinessInfoSave}
                  loading={businessLoading}
                />
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="training" className="space-y-4">
          <AITrainingManager
            botId={botId!}
            businessInfo={businessInfo || undefined}
            documents={[]} // TODO: Add documents from the documents tab
            onTrainingComplete={handleTrainingComplete}
          />
        </TabsContent>

        <TabsContent value="documents" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Document Upload</CardTitle>
              <CardDescription>
                Upload documents to provide additional context for your bot
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <FileText className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                <p className="text-muted-foreground">
                  Document upload functionality coming soon
                </p>
                <p className="text-sm text-muted-foreground mt-2">
                  For now, use the business information tab to provide context for your bot
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Instructions */}
      <Card>
        <CardHeader>
          <CardTitle>How to Train Your Bot</CardTitle>
          <CardDescription>
            Follow these steps to get the best results
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center p-4 border rounded-lg">
              <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
                <span className="text-blue-600 font-bold">1</span>
              </div>
              <h3 className="font-medium mb-2">Add Business Info</h3>
              <p className="text-sm text-muted-foreground">
                Fill in your business details, hours, services, and contact information
              </p>
            </div>
            
            <div className="text-center p-4 border rounded-lg">
              <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3">
                <span className="text-green-600 font-bold">2</span>
              </div>
              <h3 className="font-medium mb-2">Train with AI</h3>
              <p className="text-sm text-muted-foreground">
                Generate AI responses based on your business information
              </p>
            </div>
            
            <div className="text-center p-4 border rounded-lg">
              <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-3">
                <span className="text-purple-600 font-bold">3</span>
              </div>
              <h3 className="font-medium mb-2">Review & Deploy</h3>
              <p className="text-sm text-muted-foreground">
                Review generated responses and approve them for use
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default BotTraining;
