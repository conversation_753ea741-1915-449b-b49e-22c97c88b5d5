import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/hooks/use-toast';

export interface BusinessCategory {
  id: string;
  name: string;
  description: string;
}

export interface QuestionTemplate {
  id: string;
  business_category_id: string;
  question_text: string;
  question_intent: string;
  priority: number;
  is_active: boolean;
}

export interface BotResponseSchema {
  id: string;
  bot_id: string;
  question_template_id: string;
  ai_generated_response: string;
  provider_used: string;
  is_approved: boolean;
  custom_response: string | null;
  is_active: boolean;
  created_at: string;
  question_template?: QuestionTemplate;
}

export interface BotCoverage {
  bot_id: string;
  bot_name: string;
  business_category: string;
  total_questions: number;
  answered_questions: number;
  coverage_percentage: number;
}

export const useAIAnalytics = () => {
  const [usageStats, setUsageStats] = useState<AIUsageStats[]>([]);
  const [recentResponses, setRecentResponses] = useState<AIResponseWithMessage[]>([]);
  const [dailyUsage, setDailyUsage] = useState<DailyUsage[]>([]);
  const [loading, setLoading] = useState(true);
  const { user } = useAuth();
  const { toast } = useToast();

  useEffect(() => {
    if (user) {
      fetchAIAnalytics();
    }
  }, [user]);

  const fetchAIAnalytics = async () => {
    if (!user) return;

    try {
      setLoading(true);

      // Fetch usage stats using the database function
      const { data: statsData, error: statsError } = await supabase
        .rpc('get_user_ai_stats', { user_uuid: user.id });

      if (statsError) {
        console.error('Error fetching AI stats:', statsError);
      } else {
        setUsageStats(statsData || []);
      }

      // Fetch recent AI responses with message details
      const { data: responsesData, error: responsesError } = await supabase
        .from('ai_responses')
        .select(`
          *,
          message:messages!ai_responses_message_id_fkey(
            content,
            sender,
            bot:bots!messages_bot_id_fkey(name)
          )
        `)
        .order('created_at', { ascending: false })
        .limit(50);

      if (responsesError) {
        console.error('Error fetching recent responses:', responsesError);
      } else {
        // Filter responses for user's bots only
        const userResponses = responsesData?.filter(response => 
          response.message?.bot?.name // Only include responses with valid bot data
        ) || [];
        setRecentResponses(userResponses);
      }

      // Fetch daily usage for the last 30 days
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

      const { data: dailyData, error: dailyError } = await supabase
        .from('ai_response_analytics')
        .select('*')
        .gte('date', thirtyDaysAgo.toISOString().split('T')[0])
        .eq('user_id', user.id)
        .order('date', { ascending: true });

      if (dailyError) {
        console.error('Error fetching daily usage:', dailyError);
      } else {
        setDailyUsage(dailyData || []);
      }

    } catch (error) {
      console.error('Error fetching AI analytics:', error);
      toast({
        title: "Error",
        description: "Failed to load AI analytics",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const updateResponseQuality = async (responseId: string, qualityScore: number) => {
    try {
      const { error } = await supabase
        .from('ai_responses')
        .update({ response_quality_score: qualityScore })
        .eq('id', responseId);

      if (error) throw error;

      // Update local state
      setRecentResponses(prev => 
        prev.map(response => 
          response.id === responseId 
            ? { ...response, response_quality_score: qualityScore }
            : response
        )
      );

      toast({
        title: "Success",
        description: "Response quality updated",
      });

      // Refresh stats to reflect the change
      fetchAIAnalytics();

    } catch (error) {
      console.error('Error updating response quality:', error);
      toast({
        title: "Error",
        description: "Failed to update response quality",
        variant: "destructive",
      });
    }
  };

  const getTotalStats = () => {
    return usageStats.reduce((totals, stat) => ({
      total_responses: totals.total_responses + stat.total_responses,
      total_tokens: totals.total_tokens + stat.total_tokens,
      total_cost: totals.total_cost + stat.total_cost,
      avg_processing_time: (totals.avg_processing_time + stat.avg_processing_time) / 2,
      avg_quality_score: (totals.avg_quality_score + stat.avg_quality_score) / 2,
    }), {
      total_responses: 0,
      total_tokens: 0,
      total_cost: 0,
      avg_processing_time: 0,
      avg_quality_score: 0,
    });
  };

  const getProviderComparison = () => {
    return usageStats.map(stat => ({
      provider: stat.provider,
      efficiency: stat.avg_processing_time > 0 ? stat.total_responses / stat.avg_processing_time : 0,
      cost_per_response: stat.total_responses > 0 ? stat.total_cost / stat.total_responses : 0,
      quality: stat.avg_quality_score,
    }));
  };

  return {
    usageStats,
    recentResponses,
    dailyUsage,
    loading,
    updateResponseQuality,
    getTotalStats,
    getProviderComparison,
    refetch: fetchAIAnalytics,
  };
};
