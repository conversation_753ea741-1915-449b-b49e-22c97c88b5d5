import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/hooks/use-toast';
import { BusinessInfo } from '@/components/BusinessInfoForm';

export const useBusinessInfo = (botId?: string) => {
  const [businessInfo, setBusinessInfo] = useState<BusinessInfo | null>(null);
  const [loading, setLoading] = useState(true);
  const { user } = useAuth();
  const { toast } = useToast();

  useEffect(() => {
    if (botId && user) {
      fetchBusinessInfo();
    }
  }, [botId, user]);

  const fetchBusinessInfo = async () => {
    if (!botId || !user) return;

    try {
      setLoading(true);
      
      // For now, we'll store business info in the bots table
      // In a real implementation, you might want a separate business_info table
      const { data: bot, error } = await supabase
        .from('bots')
        .select('*')
        .eq('id', botId)
        .eq('user_id', user.id)
        .single();

      if (error && error.code !== 'PGRST116') {
        throw error;
      }

      if (bot && bot.business_info) {
        setBusinessInfo(bot.business_info);
      } else {
        // Set default business info structure
        setBusinessInfo({
          business_name: bot?.name || '',
          business_type: 'restaurant',
          description: '',
          address: '',
          phone: '',
          email: '',
          website: '',
          hours: {
            monday: '9:00 AM - 9:00 PM',
            tuesday: '9:00 AM - 9:00 PM',
            wednesday: '9:00 AM - 9:00 PM',
            thursday: '9:00 AM - 9:00 PM',
            friday: '9:00 AM - 10:00 PM',
            saturday: '9:00 AM - 10:00 PM',
            sunday: '10:00 AM - 8:00 PM',
          },
          services: [],
          special_info: '',
        });
      }
    } catch (error) {
      console.error('Error fetching business info:', error);
      toast({
        title: "Error",
        description: "Failed to load business information",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const saveBusinessInfo = async (info: BusinessInfo) => {
    if (!botId || !user) {
      throw new Error('Bot ID and user required');
    }

    try {
      // Update the bot with business information
      const { error } = await supabase
        .from('bots')
        .update({ 
          business_info: info,
          updated_at: new Date().toISOString()
        })
        .eq('id', botId)
        .eq('user_id', user.id);

      if (error) throw error;

      setBusinessInfo(info);
      
      console.log('✅ Business information saved successfully');
    } catch (error) {
      console.error('❌ Error saving business info:', error);
      throw error;
    }
  };

  const updateBusinessInfo = async (updates: Partial<BusinessInfo>) => {
    if (!businessInfo) return;

    const updatedInfo = { ...businessInfo, ...updates };
    await saveBusinessInfo(updatedInfo);
  };

  const getBusinessContext = (): string => {
    if (!businessInfo) return '';

    let context = `Business: ${businessInfo.business_name}\n`;
    context += `Type: ${businessInfo.business_type}\n`;
    
    if (businessInfo.description) {
      context += `Description: ${businessInfo.description}\n`;
    }
    
    if (businessInfo.address) {
      context += `Address: ${businessInfo.address}\n`;
    }
    
    if (businessInfo.phone) {
      context += `Phone: ${businessInfo.phone}\n`;
    }
    
    if (businessInfo.email) {
      context += `Email: ${businessInfo.email}\n`;
    }
    
    if (businessInfo.website) {
      context += `Website: ${businessInfo.website}\n`;
    }

    // Add hours
    context += `\nBusiness Hours:\n`;
    Object.entries(businessInfo.hours).forEach(([day, hours]) => {
      context += `${day.charAt(0).toUpperCase() + day.slice(1)}: ${hours}\n`;
    });

    // Add services
    if (businessInfo.services.length > 0) {
      context += `\nServices/Products: ${businessInfo.services.join(', ')}\n`;
    }

    // Add special info
    if (businessInfo.special_info) {
      context += `\nSpecial Information: ${businessInfo.special_info}\n`;
    }

    return context;
  };

  const isComplete = (): boolean => {
    if (!businessInfo) return false;
    
    return !!(
      businessInfo.business_name &&
      businessInfo.business_type &&
      businessInfo.description
    );
  };

  const getCompletionScore = (): number => {
    if (!businessInfo) return 0;

    let score = 0;
    const fields = [
      businessInfo.business_name,
      businessInfo.business_type,
      businessInfo.description,
      businessInfo.address,
      businessInfo.phone,
      businessInfo.email,
      businessInfo.website,
      businessInfo.services.length > 0 ? 'services' : '',
      businessInfo.special_info,
    ];

    const filledFields = fields.filter(field => field && field.trim()).length;
    score = Math.round((filledFields / fields.length) * 100);

    return score;
  };

  return {
    businessInfo,
    loading,
    saveBusinessInfo,
    updateBusinessInfo,
    getBusinessContext,
    isComplete,
    getCompletionScore,
    refetch: fetchBusinessInfo,
  };
};
