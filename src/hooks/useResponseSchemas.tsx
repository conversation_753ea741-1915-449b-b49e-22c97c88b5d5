import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/hooks/use-toast';
import { aiService } from '@/services/aiService';

export interface BusinessCategory {
  id: string;
  name: string;
  description: string;
}

export interface QuestionTemplate {
  id: string;
  business_category_id: string;
  question_text: string;
  question_intent: string;
  priority: number;
  is_active: boolean;
}

export interface BotResponseSchema {
  id: string;
  bot_id: string;
  question_template_id: string | null;
  ai_generated_response: string;
  provider_used: string;
  is_approved: boolean;
  custom_response: string | null;
  custom_question: string | null;
  custom_intent: string | null;
  is_active: boolean;
  created_at: string;
  question_template?: QuestionTemplate;
}

export interface BotCoverage {
  bot_id: string;
  bot_name: string;
  business_category: string;
  total_questions: number;
  answered_questions: number;
  coverage_percentage: number;
}

export const useResponseSchemas = (botId?: string) => {
  const [businessCategories, setBusinessCategories] = useState<BusinessCategory[]>([]);
  const [questionTemplates, setQuestionTemplates] = useState<QuestionTemplate[]>([]);
  const [responseSchemas, setResponseSchemas] = useState<BotResponseSchema[]>([]);
  const [botCoverage, setBotCoverage] = useState<BotCoverage | null>(null);
  const [loading, setLoading] = useState(true);
  const [generating, setGenerating] = useState(false);
  const { user } = useAuth();
  const { toast } = useToast();

  useEffect(() => {
    fetchBusinessCategories();
  }, []);

  useEffect(() => {
    if (botId) {
      fetchBotResponseSchemas();
      fetchBotCoverage();
    }
  }, [botId, user]);

  const fetchBusinessCategories = async () => {
    try {
      const { data, error } = await supabase
        .from('business_categories')
        .select('*')
        .order('name');

      if (error) throw error;
      setBusinessCategories(data || []);
    } catch (error) {
      console.error('Error fetching business categories:', error);
    }
  };

  const fetchQuestionTemplates = async (categoryName: string = 'restaurant') => {
    try {
      const { data, error } = await supabase
        .from('question_templates')
        .select('*')
        .eq('business_categories.name', categoryName)
        .eq('is_active', true)
        .order('priority', { ascending: true });

      if (error) throw error;
      setQuestionTemplates(data || []);
    } catch (error) {
      console.error('Error fetching question templates:', error);
    }
  };

  const fetchBotResponseSchemas = async () => {
    if (!botId || !user) return;

    try {
      setLoading(true);
      const { data, error } = await supabase
        .from('bot_response_schemas')
        .select(`
          *,
          question_template:question_templates(*)
        `)
        .eq('bot_id', botId)
        .eq('is_active', true)
        .order('created_at', { ascending: false });

      if (error) throw error;
      setResponseSchemas(data || []);
    } catch (error) {
      console.error('Error fetching bot response schemas:', error);
      toast({
        title: "Error",
        description: "Failed to load response schemas",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const fetchBotCoverage = async () => {
    if (!botId || !user) return;

    try {
      const { data, error } = await supabase
        .from('bot_response_coverage')
        .select('*')
        .eq('bot_id', botId)
        .single();

      if (error && error.code !== 'PGRST116') throw error; // Ignore "not found" error
      setBotCoverage(data);
    } catch (error) {
      console.error('Error fetching bot coverage:', error);
    }
  };

  const generateResponseSchemas = async (
    botId: string,
    businessCategory: string = 'restaurant',
    provider: string = 'openai'
  ) => {
    if (!user) return;

    setGenerating(true);
    try {
      console.log(`🤖 Generating response schemas for bot ${botId}...`);

      // Get questions that need responses
      const { data: questions, error: questionsError } = await supabase
        .rpc('generate_bot_response_schemas', {
          bot_uuid: botId,
          business_category_name: businessCategory,
          ai_provider: provider
        });

      if (questionsError) throw questionsError;

      const questionsToGenerate = questions?.filter(q => !q.schema_created) || [];
      
      if (questionsToGenerate.length === 0) {
        toast({
          title: "Info",
          description: "All questions already have response schemas",
        });
        return;
      }

      // Get bot info for context
      const { data: bot, error: botError } = await supabase
        .from('bots')
        .select('*')
        .eq('id', botId)
        .single();

      if (botError) throw botError;

      let successCount = 0;
      const schemas: any[] = [];

      // Generate responses for each question
      for (const question of questionsToGenerate) {
        try {
          console.log(`Generating response for: ${question.question_text}`);

          const aiContext = {
            botName: bot.name,
            tone: 'professional',
            language: 'English',
            systemPrompt: `You are a ${businessCategory} assistant. Provide helpful, accurate responses to customer questions. Keep responses concise but informative.`
          };

          const aiResponse = await aiService.generateResponse(
            question.question_text,
            aiContext,
            provider
          );

          schemas.push({
            bot_id: botId,
            question_template_id: question.question_id,
            ai_generated_response: aiResponse.content,
            provider_used: aiResponse.provider,
            is_approved: false,
            is_active: true
          });

          successCount++;
        } catch (error) {
          console.error(`Failed to generate response for: ${question.question_text}`, error);
        }
      }

      // Insert all schemas at once
      if (schemas.length > 0) {
        const { error: insertError } = await supabase
          .from('bot_response_schemas')
          .insert(schemas);

        if (insertError) throw insertError;
      }

      toast({
        title: "Success",
        description: `Generated ${successCount} response schemas for your bot`,
      });

      // Refresh data
      await fetchBotResponseSchemas();
      await fetchBotCoverage();

    } catch (error: any) {
      console.error('Error generating response schemas:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to generate response schemas",
        variant: "destructive",
      });
    } finally {
      setGenerating(false);
    }
  };

  const updateResponseSchema = async (
    schemaId: string,
    updates: Partial<BotResponseSchema>
  ) => {
    try {
      const { error } = await supabase
        .from('bot_response_schemas')
        .update(updates)
        .eq('id', schemaId);

      if (error) throw error;

      // Update local state
      setResponseSchemas(prev =>
        prev.map(schema =>
          schema.id === schemaId ? { ...schema, ...updates } : schema
        )
      );

      toast({
        title: "Success",
        description: "Response schema updated",
      });
    } catch (error) {
      console.error('Error updating response schema:', error);
      toast({
        title: "Error",
        description: "Failed to update response schema",
        variant: "destructive",
      });
    }
  };

  const deleteResponseSchema = async (schemaId: string) => {
    try {
      const { error } = await supabase
        .from('bot_response_schemas')
        .delete()
        .eq('id', schemaId);

      if (error) throw error;

      setResponseSchemas(prev => prev.filter(schema => schema.id !== schemaId));
      
      toast({
        title: "Success",
        description: "Response schema deleted",
      });

      // Refresh coverage
      await fetchBotCoverage();
    } catch (error) {
      console.error('Error deleting response schema:', error);
      toast({
        title: "Error",
        description: "Failed to delete response schema",
        variant: "destructive",
      });
    }
  };

  return {
    businessCategories,
    questionTemplates,
    responseSchemas,
    botCoverage,
    loading,
    generating,
    fetchQuestionTemplates,
    generateResponseSchemas,
    updateResponseSchema,
    deleteResponseSchema,
    refetch: fetchBotResponseSchemas,
  };
};
