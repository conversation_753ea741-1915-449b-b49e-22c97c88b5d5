import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/hooks/use-toast';

export interface Bot {
  id: string;
  user_id: string;
  name: string;
  channel: 'whatsapp' | 'sms' | 'messenger';
  status: 'active' | 'paused' | 'archived';
  embedding_context_id?: string | null;
  created_at: string;
  updated_at: string;
  // Optional fields for training functionality
  training_status?: 'pending' | 'processing' | 'complete' | 'failed';
  training_updated_at?: string;
}

export interface BotCreateData {
  name: string;
  channel: Bot['channel'];
  description?: string;
  tone?: string;
  language?: string;
}

export const useBots = () => {
  const [bots, setBots] = useState<Bot[]>([]);
  const [loading, setLoading] = useState(true);
  const { user } = useAuth();
  const { toast } = useToast();

  useEffect(() => {
    if (user) {
      fetchBots();
    }
  }, [user]);

  const fetchBots = async () => {
    if (!user) return;

    try {
      const { data, error } = await supabase
        .from('bots')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false });

      if (error) throw error;
      setBots(data || []);
    } catch (error) {
      console.error('Error fetching bots:', error);
      toast({
        title: "Error",
        description: "Failed to load your bots",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const createBot = async (botData: BotCreateData | { name: string; channel: Bot['channel']; [key: string]: any }) => {
    if (!user) {
      toast({
        title: "Error",
        description: "You must be logged in to create a bot",
        variant: "destructive",
      });
      return null;
    }

    // Validate input data
    if (!botData || typeof botData !== 'object') {
      toast({
        title: "Error",
        description: "Invalid bot data provided",
        variant: "destructive",
      });
      return null;
    }

    if (!botData.name || typeof botData.name !== 'string') {
      toast({
        title: "Error",
        description: "Bot name is required",
        variant: "destructive",
      });
      return null;
    }

    if (!botData.channel) {
      toast({
        title: "Error",
        description: "Bot channel is required",
        variant: "destructive",
      });
      return null;
    }

    try {
      const trimmedName = botData.name.trim();
      if (!trimmedName) {
        toast({
          title: "Error",
          description: "Bot name cannot be empty",
          variant: "destructive",
        });
        return null;
      }

      console.log('🤖 Creating bot:', {
        name: trimmedName,
        channel: botData.channel,
        user_id: user.id
      });

      const { data, error } = await supabase
        .from('bots')
        .insert({
          user_id: user.id,
          name: trimmedName,
          channel: botData.channel,
          status: 'active' as const,
        })
        .select()
        .single();

      if (error) {
        console.error('❌ Bot creation error:', error);
        throw error;
      }

      console.log('✅ Bot created successfully:', data);

      // Update local state with optimistic update
      setBots(prev => [data, ...prev]);

      toast({
        title: "Success",
        description: `Bot "${trimmedName}" created successfully!`,
      });

      return data;
    } catch (error: any) {
      console.error('❌ Error creating bot:', error);

      let errorMessage = "Failed to create bot";
      if (error?.message?.includes('duplicate key')) {
        errorMessage = "A bot with this name already exists";
      } else if (error?.message?.includes('permission')) {
        errorMessage = "You don't have permission to create bots";
      } else if (error?.message?.includes('violates row-level security')) {
        errorMessage = "Permission denied. Please check your account permissions.";
      } else if (error?.message) {
        errorMessage = error.message;
      }

      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
      return null;
    }
  };

  const updateBotStatus = async (botId: string, status: Bot['status']) => {
    try {
      const { error } = await supabase
        .from('bots')
        .update({ status })
        .eq('id', botId);

      if (error) throw error;
      
      setBots(prev => prev.map(bot => 
        bot.id === botId ? { ...bot, status } : bot
      ));
      
      toast({
        title: "Success",
        description: `Bot ${status} successfully`,
      });
    } catch (error) {
      console.error('Error updating bot status:', error);
      toast({
        title: "Error",
        description: "Failed to update bot status",
        variant: "destructive",
      });
    }
  };

  const deleteBot = async (botId: string) => {
    try {
      const { error } = await supabase
        .from('bots')
        .delete()
        .eq('id', botId);

      if (error) throw error;
      
      setBots(prev => prev.filter(bot => bot.id !== botId));
      toast({
        title: "Success",
        description: "Bot deleted successfully",
      });
    } catch (error) {
      console.error('Error deleting bot:', error);
      toast({
        title: "Error",
        description: "Failed to delete bot",
        variant: "destructive",
      });
    }
  };

  const getBotById = async (botId: string): Promise<Bot | null> => {
    try {
      const { data, error } = await supabase
        .from('bots')
        .select('*')
        .eq('id', botId)
        .single();

      if (error) {
        console.error('Error fetching bot:', error);
        return null;
      }

      return data;
    } catch (error) {
      console.error('Error fetching bot:', error);
      return null;
    }
  };

  const updateBot = async (botId: string, updates: Partial<Bot>) => {
    try {
      console.log('Updating bot:', botId, 'with updates:', updates);

      const { data, error } = await supabase
        .from('bots')
        .update(updates)
        .eq('id', botId)
        .select()
        .single();

      if (error) {
        console.error('Supabase error:', error);
        throw error;
      }

      console.log('Update successful:', data);

      // Update local state
      setBots(prev => prev.map(bot =>
        bot.id === botId ? { ...bot, ...data } : bot
      ));

      return data;
    } catch (error) {
      console.error('Error updating bot:', error);
      throw error;
    }
  };

  const retrainBot = async (botId: string) => {
    try {
      // Update training status to processing
      await updateBot(botId, {
        training_status: 'processing',
        training_updated_at: new Date().toISOString()
      });

      // TODO: Call backend API or Supabase Edge Function
      // const { data, error } = await supabase.functions.invoke('retrain-bot', {
      //   body: { bot_id: botId }
      // });

      // For now, simulate the training process
      setTimeout(async () => {
        try {
          await updateBot(botId, {
            training_status: 'complete',
            training_updated_at: new Date().toISOString()
          });

          toast({
            title: "Training Complete",
            description: "Your bot has been successfully retrained with the latest information.",
          });
        } catch (error) {
          await updateBot(botId, {
            training_status: 'failed',
            training_updated_at: new Date().toISOString()
          });

          toast({
            title: "Training Failed",
            description: "Bot training failed. Please try again.",
            variant: "destructive",
          });
        }
      }, 3000); // Simulate 3 second training

      return true;
    } catch (error) {
      console.error('Error retraining bot:', error);
      await updateBot(botId, {
        training_status: 'failed',
        training_updated_at: new Date().toISOString()
      });

      toast({
        title: "Training Failed",
        description: "Failed to start bot training. Please try again.",
        variant: "destructive",
      });
      return false;
    }
  };

  const getBotsByChannel = (channel: Bot['channel']) => {
    return bots.filter(bot => bot.channel === channel);
  };

  const getActiveBots = () => {
    return bots.filter(bot => bot.status === 'active');
  };

  return {
    bots,
    loading,
    createBot,
    updateBotStatus,
    deleteBot,
    getBotById,
    updateBot,
    retrainBot,
    getBotsByChannel,
    getActiveBots,
    refetch: fetchBots
  };
};