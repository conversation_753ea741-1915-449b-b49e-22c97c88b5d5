import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/hooks/use-toast';
import { aiService, AIContext } from '@/services/aiService';
import { useBots } from '@/hooks/useBots';

export interface Message {
  id: string;
  bot_id: string;
  sender: 'user' | 'bot';
  content: string;
  timestamp: string;
  channel: 'whatsapp' | 'sms' | 'messenger';
  recipient_number?: string;
  created_at: string;
}

export interface MessageWithBot extends Message {
  bot?: {
    id: string;
    name: string;
    channel: string;
  };
}

export const useMessages = (botId?: string) => {
  const [messages, setMessages] = useState<MessageWithBot[]>([]);
  const [loading, setLoading] = useState(true);
  const [aiLoading, setAiLoading] = useState(false);
  const { user } = useAuth();
  const { toast } = useToast();
  const { getBotById } = useBots();

  useEffect(() => {
    if (user) {
      fetchMessages();
    }
  }, [user, botId]);

  const fetchMessages = async () => {
    if (!user) return;

    try {
      let query = supabase
        .from('messages')
        .select(`
          *,
          bot:bots!messages_bot_id_fkey(id, name, channel)
        `)
        .order('timestamp', { ascending: false });

      if (botId && botId !== 'all') {
        query = query.eq('bot_id', botId);
      }

      const { data, error } = await query.limit(100);

      if (error) throw error;
      setMessages(data || []);
    } catch (error) {
      console.error('Error fetching messages:', error);
      toast({
        title: "Error",
        description: "Failed to load messages",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const sendMessage = async (
    botId: string,
    content: string,
    sender: 'user' | 'bot',
    channel: Message['channel'],
    recipientNumber?: string
  ) => {
    try {
      const { data, error } = await supabase
        .from('messages')
        .insert({
          bot_id: botId,
          sender,
          content,
          channel,
          recipient_number: recipientNumber,
          timestamp: new Date().toISOString()
        })
        .select()
        .single();

      if (error) throw error;

      setMessages(prev => [data, ...prev]);
      return data;
    } catch (error) {
      console.error('Error sending message:', error);
      toast({
        title: "Error",
        description: "Failed to send message",
        variant: "destructive",
      });
      return null;
    }
  };



  const sendUserMessageWithAIResponse = async (
    botId: string,
    userMessage: string,
    channel: Message['channel'],
    recipientNumber?: string
  ) => {
    if (!user) {
      toast({
        title: "Error",
        description: "You must be logged in to send messages",
        variant: "destructive",
      });
      return null;
    }

    setAiLoading(true);

    try {
      console.log('💬 Sending user message and generating AI response...');

      // 1. Send user message
      const userMessageData = await sendMessage(botId, userMessage, 'user', channel, recipientNumber);
      if (!userMessageData) {
        throw new Error('Failed to send user message');
      }

      // 2. Get bot information for context
      const bot = await getBotById(botId);
      if (!bot) {
        throw new Error('Bot not found');
      }

      // 3. Build conversation context
      const recentMessages = messages
        .filter(msg => msg.bot_id === botId)
        .slice(0, 10) // Last 10 messages for context
        .reverse() // Chronological order
        .map(msg => ({
          role: msg.sender === 'user' ? 'user' as const : 'assistant' as const,
          content: msg.content
        }));

      const aiContext: AIContext = {
        botName: bot.name,
        tone: 'friendly', // You can get this from bot settings
        language: 'English', // You can get this from bot settings
        conversationHistory: recentMessages,
        systemPrompt: `You are ${bot.name}, a helpful assistant for ${channel} conversations.`
      };

      // 4. Generate AI response
      console.log('🤖 Generating AI response...');
      const aiResponse = await aiService.generateResponse(userMessage, aiContext);

      // 5. Send AI response as bot message
      const botMessageData = await sendMessage(
        botId,
        aiResponse.content,
        'bot',
        channel,
        recipientNumber
      );

      // AI response metadata is now handled by the response schema system

      if (botMessageData) {
        console.log('✅ AI conversation completed successfully');
        toast({
          title: "Message Sent",
          description: "Your message was sent and the bot responded!",
        });
      }

      return {
        userMessage: userMessageData,
        botMessage: botMessageData,
        aiResponse
      };

    } catch (error: any) {
      console.error('❌ Error in AI conversation:', error);

      let errorMessage = "Failed to process your message";
      if (error.message?.includes('AI provider')) {
        errorMessage = "AI service is currently unavailable";
      } else if (error.message?.includes('Bot not found')) {
        errorMessage = "Selected bot is not available";
      }

      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
      return null;
    } finally {
      setAiLoading(false);
    }
  };

  return {
    messages,
    loading,
    aiLoading,
    sendMessage,
    sendUserMessageWithAIResponse,
    refetch: fetchMessages
  };
};