-- Migration to add Response Schema System
-- Run this in your Supabase SQL editor

-- Create business categories for different use cases
CREATE TABLE IF NOT EXISTS business_categories (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name VARCHAR(100) NOT NULL UNIQUE, -- 'restaurant', 'retail', 'healthcare', etc.
  description TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Insert default business categories
INSERT INTO business_categories (name, description) VALUES
  ('restaurant', 'Restaurants, cafes, food delivery services'),
  ('retail', 'Shops, e-commerce, product sales'),
  ('healthcare', 'Clinics, hospitals, medical services'),
  ('real_estate', 'Property sales, rentals, real estate agencies'),
  ('automotive', 'Car dealerships, auto repair, vehicle services'),
  ('beauty_wellness', 'Salons, spas, fitness centers'),
  ('education', 'Schools, training centers, online courses'),
  ('professional_services', 'Legal, accounting, consulting'),
  ('hospitality', 'Hotels, travel, tourism'),
  ('general', 'General business inquiries')
ON CONFLICT (name) DO NOTHING;

-- Create expected questions templates for each business category
CREATE TABLE IF NOT EXISTS question_templates (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  business_category_id UUID REFERENCES business_categories(id) ON DELETE CASCADE,
  question_text TEXT NOT NULL,
  question_intent VARCHAR(100), -- 'menu_inquiry', 'booking', 'pricing', etc.
  priority INTEGER DEFAULT 1, -- 1=high, 2=medium, 3=low
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Insert default question templates for restaurants
INSERT INTO question_templates (business_category_id, question_text, question_intent, priority)
SELECT
  bc.id,
  question_data.question_text,
  question_data.question_intent,
  question_data.priority
FROM business_categories bc
CROSS JOIN (VALUES
  ('What cuisines do you have?', 'menu_inquiry', 1),
  ('Can I see your menu?', 'menu_inquiry', 1),
  ('What are your opening hours?', 'hours_inquiry', 1),
  ('Can I make a reservation?', 'booking', 1),
  ('Do you deliver?', 'delivery_inquiry', 1),
  ('What are your prices?', 'pricing_inquiry', 2),
  ('Do you have vegetarian options?', 'dietary_inquiry', 2),
  ('Where are you located?', 'location_inquiry', 2),
  ('Do you accept credit cards?', 'payment_inquiry', 3),
  ('Can I cancel my order?', 'order_management', 2)
) AS question_data(question_text, question_intent, priority)
WHERE bc.name = 'restaurant';

-- Create bot response schemas - pre-generated responses for each bot
CREATE TABLE IF NOT EXISTS bot_response_schemas (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  bot_id UUID REFERENCES bots(id) ON DELETE CASCADE,
  question_template_id UUID REFERENCES question_templates(id) ON DELETE CASCADE,
  ai_generated_response TEXT NOT NULL,
  provider_used VARCHAR(50), -- 'openai', 'gemini', 'mock'
  is_approved BOOLEAN DEFAULT false, -- Bot owner can approve/edit responses
  custom_response TEXT, -- Bot owner can customize the AI response
  custom_question TEXT, -- For business-specific questions not in templates
  custom_intent VARCHAR(100), -- Intent for custom questions
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Remove the unique constraint to allow custom questions
-- UNIQUE(bot_id, question_template_id) -- Removed to allow custom questions

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_question_templates_category ON question_templates(business_category_id);
CREATE INDEX IF NOT EXISTS idx_question_templates_intent ON question_templates(question_intent);
CREATE INDEX IF NOT EXISTS idx_bot_response_schemas_bot ON bot_response_schemas(bot_id);
CREATE INDEX IF NOT EXISTS idx_bot_response_schemas_template ON bot_response_schemas(question_template_id);

-- Add RLS (Row Level Security)
ALTER TABLE business_categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE question_templates ENABLE ROW LEVEL SECURITY;
ALTER TABLE bot_response_schemas ENABLE ROW LEVEL SECURITY;

-- Policies for business_categories (public read)
CREATE POLICY "Anyone can view business categories" ON business_categories
  FOR SELECT USING (true);

-- Policies for question_templates (public read)
CREATE POLICY "Anyone can view question templates" ON question_templates
  FOR SELECT USING (true);

-- Policies for bot_response_schemas
CREATE POLICY "Users can view their own bot response schemas" ON bot_response_schemas
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM bots b
      WHERE b.id = bot_response_schemas.bot_id
      AND b.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can insert response schemas for their own bots" ON bot_response_schemas
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM bots b
      WHERE b.id = bot_response_schemas.bot_id
      AND b.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can update their own bot response schemas" ON bot_response_schemas
  FOR UPDATE USING (
    EXISTS (
      SELECT 1 FROM bots b
      WHERE b.id = bot_response_schemas.bot_id
      AND b.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can delete their own bot response schemas" ON bot_response_schemas
  FOR DELETE USING (
    EXISTS (
      SELECT 1 FROM bots b
      WHERE b.id = bot_response_schemas.bot_id
      AND b.user_id = auth.uid()
    )
  );

-- Add updated_at trigger
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_bot_response_schemas_updated_at
  BEFORE UPDATE ON bot_response_schemas
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Create useful views
CREATE OR REPLACE VIEW bot_response_coverage AS
SELECT
  b.id as bot_id,
  b.name as bot_name,
  b.user_id,
  bc.name as business_category,
  COUNT(qt.id) as total_questions,
  COUNT(brs.id) as answered_questions,
  ROUND((COUNT(brs.id)::DECIMAL / COUNT(qt.id)) * 100, 2) as coverage_percentage
FROM bots b
LEFT JOIN business_categories bc ON bc.name = 'restaurant' -- Default for now, can be made dynamic
LEFT JOIN question_templates qt ON qt.business_category_id = bc.id AND qt.is_active = true
LEFT JOIN bot_response_schemas brs ON brs.bot_id = b.id AND brs.question_template_id = qt.id AND brs.is_active = true
GROUP BY b.id, b.name, b.user_id, bc.name;

-- RLS for the view
ALTER VIEW bot_response_coverage OWNER TO postgres;
GRANT SELECT ON bot_response_coverage TO authenticated;

-- Function to generate response schemas for a bot
CREATE OR REPLACE FUNCTION generate_bot_response_schemas(
  bot_uuid UUID,
  business_category_name VARCHAR(100) DEFAULT 'restaurant',
  ai_provider VARCHAR(50) DEFAULT 'openai'
)
RETURNS TABLE (
  question_id UUID,
  question_text TEXT,
  question_intent VARCHAR(100),
  schema_created BOOLEAN
) AS $$
DECLARE
  category_id UUID;
BEGIN
  -- Get business category ID
  SELECT id INTO category_id
  FROM business_categories
  WHERE name = business_category_name;

  IF category_id IS NULL THEN
    RAISE EXCEPTION 'Business category % not found', business_category_name;
  END IF;

  -- Return questions that need response schemas
  RETURN QUERY
  SELECT
    qt.id as question_id,
    qt.question_text,
    qt.question_intent,
    (brs.id IS NOT NULL) as schema_created
  FROM question_templates qt
  LEFT JOIN bot_response_schemas brs ON brs.question_template_id = qt.id AND brs.bot_id = bot_uuid
  WHERE qt.business_category_id = category_id
    AND qt.is_active = true
  ORDER BY qt.priority, qt.question_text;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission
GRANT EXECUTE ON FUNCTION generate_bot_response_schemas(UUID, VARCHAR(100), VARCHAR(50)) TO authenticated;

-- Add business_info column to bots table
ALTER TABLE bots ADD COLUMN IF NOT EXISTS business_info JSONB;

-- Add custom question fields to bot_response_schemas table
ALTER TABLE bot_response_schemas ADD COLUMN IF NOT EXISTS custom_question TEXT;
ALTER TABLE bot_response_schemas ADD COLUMN IF NOT EXISTS custom_intent VARCHAR(100);

-- Make question_template_id nullable for custom questions
ALTER TABLE bot_response_schemas ALTER COLUMN question_template_id DROP NOT NULL;

-- Add CTA (Call-to-Action) payload support
ALTER TABLE bot_response_schemas ADD COLUMN IF NOT EXISTS cta_buttons JSONB;
ALTER TABLE bot_response_schemas ADD COLUMN IF NOT EXISTS cta_type VARCHAR(50) DEFAULT 'none'; -- 'none', 'buttons', 'quick_replies', 'url', 'phone'

-- Add indexes for CTA fields
CREATE INDEX IF NOT EXISTS idx_bot_response_schemas_cta_type ON bot_response_schemas(cta_type);

-- Add comments for CTA fields
COMMENT ON COLUMN bot_response_schemas.cta_buttons IS 'JSON array of CTA buttons: [{"text": "Book Now", "type": "url", "value": "https://...", "whatsapp_type": "url"}]';
COMMENT ON COLUMN bot_response_schemas.cta_type IS 'Type of CTA: none, buttons, quick_replies, url, phone';
