# Core FastAPI dependencies
fastapi==0.104.1
uvicorn[standard]==0.24.0
python-dotenv==1.0.0
pydantic==2.5.0
pydantic-settings==2.1.0
pydantic[email]

# Database
sqlalchemy==2.0.23
alembic==1.13.1
psycopg2-binary==2.9.9

# Authentication & Security
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4

# File handling
python-multipart==0.0.6
aiofiles==23.2.1

# HTTP client - compatible with google-genai
httpx>=0.28.1,<1.0.0

# WebSockets - compatible with google-genai
websockets>=13.0.0,<15.1.0

# Async support - compatible with google-genai
anyio>=3.7.1

# Testing
pytest==7.4.3
pytest-asyncio==0.21.1

# Supabase not needed for FastAPI backend
# supabase==2.3.4
