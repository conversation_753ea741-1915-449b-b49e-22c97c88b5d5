from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON>umn, ForeignKey, String, DateTime, Text, Enum
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.db.base_class import Base
import enum


class BotChannel(enum.Enum):
    whatsapp = "whatsapp"
    sms = "sms"
    messenger = "messenger"


class BotStatus(enum.Enum):
    active = "active"
    paused = "paused"
    archived = "archived"


class TrainingStatus(enum.Enum):
    pending = "pending"
    processing = "processing"
    complete = "complete"
    failed = "failed"


class Bot(Base):
    id = Column(String, primary_key=True, index=True)  # UUID
    user_id = Column(String, ForeignKey("user.id"), nullable=False)
    name = Column(String, index=True, nullable=False)
    description = Column(Text)
    channel = Column(Enum(BotChannel), nullable=False)
    status = Column(Enum(BotStatus), default=BotStatus.active)
    tone = Column(String)
    language = Column(String)
    training_status = Column(Enum(TrainingStatus), default=TrainingStatus.pending)
    training_updated_at = Column(DateTime(timezone=True))
    embedding_context_id = Column(String)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    owner = relationship("User", back_populates="bots")
    messages = relationship("Message", back_populates="bot")
    documents = relationship("Document", back_populates="bot")
