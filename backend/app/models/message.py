from sqlalchemy import Column, ForeignKey, String, DateTime, Text, Enum
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.db.base_class import Base
import enum


class MessageSender(enum.Enum):
    user = "user"
    bot = "bot"


class MessageChannel(enum.Enum):
    whatsapp = "whatsapp"
    sms = "sms"
    messenger = "messenger"


class Message(Base):
    id = Column(String, primary_key=True, index=True)  # UUID
    bot_id = Column(String, ForeignKey("bot.id"), nullable=False)
    user_id = Column(String, ForeignKey("user.id"), nullable=False)
    sender = Column(Enum(MessageSender), nullable=False)
    content = Column(Text, nullable=False)
    timestamp = Column(DateTime(timezone=True), server_default=func.now())
    channel = Column(Enum(MessageChannel), nullable=False)
    recipient_number = Column(String)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    bot = relationship("Bot", back_populates="messages")
    user = relationship("User", back_populates="messages")
