from sqlalchemy import Column, ForeignKey, String, DateTime, Enum
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.db.base_class import Base
import enum


class DocumentType(enum.Enum):
    pdf = "pdf"
    txt = "txt"
    docx = "docx"
    chat = "chat"


class EmbeddingStatus(enum.Enum):
    pending = "pending"
    processed = "processed"
    failed = "failed"


class Document(Base):
    id = Column(String, primary_key=True, index=True)  # UUID
    user_id = Column(String, ForeignKey("user.id"), nullable=False)
    bot_id = Column(String, ForeignKey("bot.id"))
    file_name = Column(String, nullable=False)
    file_url = Column(String)
    type = Column(Enum(DocumentType), nullable=False)
    embedding_status = Column(Enum(EmbeddingStatus), default=EmbeddingStatus.pending)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    user = relationship("User", back_populates="documents")
    bot = relationship("Bot", back_populates="documents")
