from sqlalchemy import <PERSON><PERSON><PERSON>, Column, String, Integer, DateTime, Enum
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.db.base_class import Base
import enum


class PlanType(enum.Enum):
    free = "free"
    pro = "pro"
    business = "business"


class User(Base):
    id = Column(String, primary_key=True, index=True)  # UUID from Supabase
    first_name = Column(String, index=True)
    last_name = Column(String, index=True)
    full_name = Column(String, index=True)
    email = Column(String, unique=True, index=True, nullable=False)
    phone_number = Column(String)
    plan = Column(Enum(PlanType), default=PlanType.free)
    tokens_used = Column(Integer, default=0)
    is_active = Column(Boolean(), default=True)
    is_superuser = Column(Boolean(), default=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    bots = relationship("Bot", back_populates="owner")
    messages = relationship("Message", back_populates="user")
    documents = relationship("Document", back_populates="user")
