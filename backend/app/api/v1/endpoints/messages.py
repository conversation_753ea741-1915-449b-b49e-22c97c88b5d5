from typing import Any, List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from datetime import datetime
import uuid

from app.schemas.message import Message, MessageCreate, MessageWithBot, BotInfo
from app.api import deps
from app.models.user import User as UserModel

router = APIRouter()


@router.get("/", response_model=List[MessageWithBot])
async def read_messages(
    bot_id: Optional[str] = Query(None, description="Filter by bot ID"),
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(deps.get_db),
    current_user: UserModel = Depends(deps.get_current_active_user),
) -> Any:
    """
    Retrieve messages for the current user.
    """
    # For demo user, return mock messages
    if current_user.id == "demo-user-id":
        mock_messages = [
            MessageWithBot(
                id="msg-1",
                bot_id="bot-1",
                sender="user",
                content="Hello, I need help with my order",
                timestamp=datetime.now(),
                channel="whatsapp",
                recipient_number="+1234567890",
                created_at=datetime.now(),
                bot=BotInfo(id="bot-1", name="Customer Support Bot", channel="whatsapp")
            ),
            MessageWithBot(
                id="msg-2",
                bot_id="bot-1",
                sender="bot",
                content="Hi! I'd be happy to help you with your order. Can you please provide your order number?",
                timestamp=datetime.now(),
                channel="whatsapp",
                recipient_number="+1234567890",
                created_at=datetime.now(),
                bot=BotInfo(id="bot-1", name="Customer Support Bot", channel="whatsapp")
            ),
            MessageWithBot(
                id="msg-3",
                bot_id="bot-2",
                sender="user",
                content="What products do you have available?",
                timestamp=datetime.now(),
                channel="messenger",
                recipient_number=None,
                created_at=datetime.now(),
                bot=BotInfo(id="bot-2", name="Sales Assistant", channel="messenger")
            ),
            MessageWithBot(
                id="msg-4",
                bot_id="bot-2",
                sender="bot",
                content="We have a wide range of products! Here are our main categories: Electronics, Clothing, Home & Garden, and Sports. What are you interested in?",
                timestamp=datetime.now(),
                channel="messenger",
                recipient_number=None,
                created_at=datetime.now(),
                bot=BotInfo(id="bot-2", name="Sales Assistant", channel="messenger")
            )
        ]
        
        # Filter by bot_id if provided
        if bot_id and bot_id != "all":
            mock_messages = [msg for msg in mock_messages if msg.bot_id == bot_id]
        
        return mock_messages[:limit]
    
    # TODO: Implement real message retrieval from database
    # query = db.query(models.Message).filter(models.Message.user_id == current_user.id)
    # if bot_id and bot_id != "all":
    #     query = query.filter(models.Message.bot_id == bot_id)
    # messages = query.order_by(models.Message.timestamp.desc()).offset(skip).limit(limit).all()
    # return messages
    
    return []


@router.post("/", response_model=Message)
async def create_message(
    *,
    message_in: MessageCreate,
    db: Session = Depends(deps.get_db),
    current_user: UserModel = Depends(deps.get_current_active_user),
) -> Any:
    """
    Create a new message.
    """
    # For demo user, return a mock created message
    if current_user.id == "demo-user-id":
        new_message = Message(
            id=str(uuid.uuid4()),
            bot_id=message_in.bot_id,
            sender=message_in.sender,
            content=message_in.content,
            timestamp=datetime.now(),
            channel=message_in.channel,
            recipient_number=message_in.recipient_number,
            created_at=datetime.now()
        )
        return new_message
    
    # TODO: Implement real message creation
    # message = crud.message.create_with_user(db, obj_in=message_in, user_id=current_user.id)
    # return message
    
    raise HTTPException(
        status_code=status.HTTP_501_NOT_IMPLEMENTED,
        detail="Message creation not implemented yet"
    )


@router.get("/{message_id}", response_model=Message)
async def read_message(
    *,
    message_id: str,
    db: Session = Depends(deps.get_db),
    current_user: UserModel = Depends(deps.get_current_active_user),
) -> Any:
    """
    Get message by ID.
    """
    # TODO: Implement real message retrieval by ID
    # message = crud.message.get(db, id=message_id)
    # if not message:
    #     raise HTTPException(status_code=404, detail="Message not found")
    # if message.user_id != current_user.id:
    #     raise HTTPException(status_code=403, detail="Not enough permissions")
    # return message
    
    raise HTTPException(status_code=404, detail="Message not found")


@router.delete("/{message_id}")
async def delete_message(
    *,
    message_id: str,
    db: Session = Depends(deps.get_db),
    current_user: UserModel = Depends(deps.get_current_active_user),
) -> Any:
    """
    Delete a message.
    """
    # TODO: Implement real message deletion
    # message = crud.message.get(db, id=message_id)
    # if not message:
    #     raise HTTPException(status_code=404, detail="Message not found")
    # if message.user_id != current_user.id:
    #     raise HTTPException(status_code=403, detail="Not enough permissions")
    # message = crud.message.remove(db, id=message_id)
    # return {"message": "Message deleted successfully"}
    
    raise HTTPException(status_code=404, detail="Message not found")
