from typing import Any, List
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from app.schemas.user import User, UserCreate, UserUpdate, UserProfile
from app.api import deps
from app import crud
from app.models.user import User as UserModel

router = APIRouter()


@router.get("/", response_model=List[User])
async def read_users(
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(deps.get_db),
    current_user: UserModel = Depends(deps.get_current_active_user),
) -> Any:
    """
    Retrieve users. (Admin only)
    """
    if not current_user.is_superuser:
        raise HTTPException(status_code=403, detail="Not enough permissions")

    # TODO: Implement user retrieval logic
    # users = crud.user.get_multi(db, skip=skip, limit=limit)
    # return users
    return []


@router.post("/", response_model=User)
async def create_user(
    *,
    user_in: User<PERSON><PERSON>,
    db: Session = Depends(deps.get_db),
    current_user: UserModel = Depends(deps.get_current_active_user),
) -> Any:
    """
    Create new user. (Admin only)
    """
    if not current_user.is_superuser:
        raise HTTPException(status_code=403, detail="Not enough permissions")

    # TODO: Implement user creation logic
    # user = crud.user.create(db, obj_in=user_in)
    # return user
    raise HTTPException(
        status_code=status.HTTP_501_NOT_IMPLEMENTED,
        detail="User creation not implemented yet"
    )


@router.get("/me", response_model=UserProfile)
async def read_user_me(
    db: Session = Depends(deps.get_db),
    current_user: UserModel = Depends(deps.get_current_active_user),
) -> Any:
    """
    Get current user profile.
    """
    # Return demo user profile for testing
    if current_user.id == "demo-user-id":
        return UserProfile(
            id="demo-user-id",
            first_name="Demo",
            last_name="User",
            full_name="Demo User",
            email="<EMAIL>",
            phone_number=None,
            plan="free",
            tokens_used=150,
            created_at="2024-01-01T00:00:00Z",
            updated_at="2024-01-01T00:00:00Z"
        )

    # TODO: Implement real user profile retrieval
    # return current_user
    raise HTTPException(
        status_code=status.HTTP_501_NOT_IMPLEMENTED,
        detail="User profile retrieval not implemented yet"
    )


@router.put("/me", response_model=UserProfile)
async def update_user_me(
    *,
    user_in: UserUpdate,
    db: Session = Depends(deps.get_db),
    current_user: UserModel = Depends(deps.get_current_active_user),
) -> Any:
    """
    Update own user profile.
    """
    # TODO: Implement user update logic
    # user = crud.user.update(db, db_obj=current_user, obj_in=user_in)
    # return user
    raise HTTPException(
        status_code=status.HTTP_501_NOT_IMPLEMENTED,
        detail="User update not implemented yet"
    )
