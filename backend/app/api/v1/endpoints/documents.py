from typing import Any, List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, UploadFile, File, Form, Query
from sqlalchemy.orm import Session
from datetime import datetime
import uuid
import aiofiles
import os

from app.schemas.document import Document, DocumentCreate, DocumentUpdate
from app.api import deps
from app.models.user import User as UserModel

router = APIRouter()


@router.get("/", response_model=List[Document])
async def read_documents(
    bot_id: Optional[str] = Query(None, description="Filter by bot ID"),
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(deps.get_db),
    current_user: UserModel = Depends(deps.get_current_active_user),
) -> Any:
    """
    Retrieve documents for the current user.
    """
    # For demo user, return mock documents
    if current_user.id == "demo-user-id":
        mock_documents = [
            Document(
                id="doc-1",
                user_id="demo-user-id",
                bot_id="bot-1",
                file_name="customer_support_faq.pdf",
                file_url="documents/demo-user-id/customer_support_faq.pdf",
                type="pdf",
                embedding_status="processed",
                created_at=datetime.now()
            ),
            Document(
                id="doc-2",
                user_id="demo-user-id",
                bot_id="bot-1",
                file_name="product_catalog.txt",
                file_url="documents/demo-user-id/product_catalog.txt",
                type="txt",
                embedding_status="processed",
                created_at=datetime.now()
            ),
            Document(
                id="doc-3",
                user_id="demo-user-id",
                bot_id="bot-2",
                file_name="sales_training.docx",
                file_url="documents/demo-user-id/sales_training.docx",
                type="docx",
                embedding_status="pending",
                created_at=datetime.now()
            )
        ]
        
        # Filter by bot_id if provided
        if bot_id:
            mock_documents = [doc for doc in mock_documents if doc.bot_id == bot_id]
        
        return mock_documents[:limit]
    
    # TODO: Implement real document retrieval from database
    # query = db.query(models.Document).filter(models.Document.user_id == current_user.id)
    # if bot_id:
    #     query = query.filter(models.Document.bot_id == bot_id)
    # documents = query.order_by(models.Document.created_at.desc()).offset(skip).limit(limit).all()
    # return documents
    
    return []


@router.post("/upload", response_model=Document)
async def upload_document(
    file: UploadFile = File(...),
    bot_id: Optional[str] = Form(None),
    db: Session = Depends(deps.get_db),
    current_user: UserModel = Depends(deps.get_current_active_user),
) -> Any:
    """
    Upload a document file.
    """
    # Validate file type
    allowed_extensions = {".pdf", ".txt", ".docx"}
    file_extension = os.path.splitext(file.filename)[1].lower()
    
    if file_extension not in allowed_extensions:
        raise HTTPException(
            status_code=400,
            detail=f"File type {file_extension} not allowed. Allowed types: {', '.join(allowed_extensions)}"
        )
    
    # Determine document type
    doc_type_map = {".pdf": "pdf", ".txt": "txt", ".docx": "docx"}
    doc_type = doc_type_map[file_extension]
    
    # For demo user, return a mock uploaded document
    if current_user.id == "demo-user-id":
        # In a real implementation, you would save the file to storage
        # file_path = f"documents/{current_user.id}/{uuid.uuid4()}-{file.filename}"
        # async with aiofiles.open(file_path, 'wb') as f:
        #     content = await file.read()
        #     await f.write(content)
        
        new_document = Document(
            id=str(uuid.uuid4()),
            user_id=current_user.id,
            bot_id=bot_id,
            file_name=file.filename,
            file_url=f"documents/{current_user.id}/{file.filename}",
            type=doc_type,
            embedding_status="pending",
            created_at=datetime.now()
        )
        return new_document
    
    # TODO: Implement real file upload and document creation
    # Save file to storage (local filesystem, S3, etc.)
    # Create document record in database
    # Trigger embedding processing
    
    raise HTTPException(
        status_code=status.HTTP_501_NOT_IMPLEMENTED,
        detail="Document upload not implemented yet"
    )


@router.post("/", response_model=Document)
async def create_document(
    *,
    document_in: DocumentCreate,
    db: Session = Depends(deps.get_db),
    current_user: UserModel = Depends(deps.get_current_active_user),
) -> Any:
    """
    Create a document record (for chat context or other text content).
    """
    # For demo user, return a mock created document
    if current_user.id == "demo-user-id":
        new_document = Document(
            id=str(uuid.uuid4()),
            user_id=current_user.id,
            bot_id=document_in.bot_id,
            file_name=document_in.file_name,
            file_url=document_in.file_url,
            type=document_in.type,
            embedding_status="pending",
            created_at=datetime.now()
        )
        return new_document
    
    # TODO: Implement real document creation
    # document = crud.document.create_with_user(db, obj_in=document_in, user_id=current_user.id)
    # return document
    
    raise HTTPException(
        status_code=status.HTTP_501_NOT_IMPLEMENTED,
        detail="Document creation not implemented yet"
    )


@router.get("/{document_id}", response_model=Document)
async def read_document(
    *,
    document_id: str,
    db: Session = Depends(deps.get_db),
    current_user: UserModel = Depends(deps.get_current_active_user),
) -> Any:
    """
    Get document by ID.
    """
    # TODO: Implement real document retrieval by ID
    # document = crud.document.get(db, id=document_id)
    # if not document:
    #     raise HTTPException(status_code=404, detail="Document not found")
    # if document.user_id != current_user.id:
    #     raise HTTPException(status_code=403, detail="Not enough permissions")
    # return document
    
    raise HTTPException(status_code=404, detail="Document not found")


@router.put("/{document_id}", response_model=Document)
async def update_document(
    *,
    document_id: str,
    document_in: DocumentUpdate,
    db: Session = Depends(deps.get_db),
    current_user: UserModel = Depends(deps.get_current_active_user),
) -> Any:
    """
    Update a document.
    """
    # TODO: Implement real document update
    # document = crud.document.get(db, id=document_id)
    # if not document:
    #     raise HTTPException(status_code=404, detail="Document not found")
    # if document.user_id != current_user.id:
    #     raise HTTPException(status_code=403, detail="Not enough permissions")
    # document = crud.document.update(db, db_obj=document, obj_in=document_in)
    # return document
    
    raise HTTPException(status_code=404, detail="Document not found")


@router.delete("/{document_id}")
async def delete_document(
    *,
    document_id: str,
    db: Session = Depends(deps.get_db),
    current_user: UserModel = Depends(deps.get_current_active_user),
) -> Any:
    """
    Delete a document.
    """
    # For demo user, just return success
    if current_user.id == "demo-user-id":
        return {"message": "Document deleted successfully"}
    
    # TODO: Implement real document deletion
    # document = crud.document.get(db, id=document_id)
    # if not document:
    #     raise HTTPException(status_code=404, detail="Document not found")
    # if document.user_id != current_user.id:
    #     raise HTTPException(status_code=403, detail="Not enough permissions")
    # Delete file from storage
    # document = crud.document.remove(db, id=document_id)
    # return {"message": "Document deleted successfully"}
    
    raise HTTPException(status_code=404, detail="Document not found")
