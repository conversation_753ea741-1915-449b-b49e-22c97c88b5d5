from typing import Generator
from fastapi import Depends, HTTPException, status
from fastapi.security import OA<PERSON>2PasswordBearer
from jose import jwt
from pydantic import ValidationError
from sqlalchemy.orm import Session
from datetime import datetime

from app import schemas
from app.models.user import User as UserModel, PlanType
from app.core import security
from app.core.config import settings
from app.db.session import SessionLocal

reusable_oauth2 = OAuth2PasswordBearer(
    tokenUrl=f"{settings.API_V1_STR}/auth/login"
)


def get_db() -> Generator:
    try:
        db = SessionLocal()
        yield db
    finally:
        db.close()


def get_current_user(
    db: Session = Depends(get_db), token: str = Depends(reusable_oauth2)
) -> UserModel:
    try:
        payload = jwt.decode(
            token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM]
        )
        token_data = schemas.TokenPayload(**payload)
    except (jwt.JWTError, ValidationError):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Could not validate credentials",
        )
    # For demo purposes, return a mock user
    if token_data.sub == "demo-user-id":
        demo_user = UserModel()
        demo_user.id = "demo-user-id"
        demo_user.email = "<EMAIL>"
        demo_user.first_name = "Demo"
        demo_user.last_name = "User"
        demo_user.full_name = "Demo User"
        demo_user.plan = PlanType.free
        demo_user.tokens_used = 150
        demo_user.is_active = True
        demo_user.is_superuser = False
        demo_user.created_at = datetime.now()
        demo_user.updated_at = datetime.now()
        return demo_user

    # TODO: Implement real user retrieval from database
    # user = crud.user.get(db, id=token_data.sub)
    # if not user:
    #     raise HTTPException(status_code=404, detail="User not found")
    # return user

    raise HTTPException(
        status_code=status.HTTP_404_NOT_FOUND,
        detail="User not found"
    )


def get_current_active_user(
    current_user: UserModel = Depends(get_current_user),
) -> UserModel:
    if not current_user.is_active:
        raise HTTPException(status_code=400, detail="Inactive user")
    return current_user
