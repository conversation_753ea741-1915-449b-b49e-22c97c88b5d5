from typing import Optional
from pydantic import BaseModel
from datetime import datetime
from enum import Enum


class MessageSender(str, Enum):
    user = "user"
    bot = "bot"


class MessageChannel(str, Enum):
    whatsapp = "whatsapp"
    sms = "sms"
    messenger = "messenger"


class MessageBase(BaseModel):
    content: str
    sender: MessageSender
    channel: MessageChannel
    recipient_number: Optional[str] = None


class MessageCreate(MessageBase):
    bot_id: str


class MessageUpdate(BaseModel):
    content: Optional[str] = None


class BotInfo(BaseModel):
    id: str
    name: str
    channel: str


class Message(BaseModel):
    id: str
    bot_id: str
    sender: MessageSender
    content: str
    timestamp: datetime
    channel: MessageChannel
    recipient_number: Optional[str] = None
    created_at: datetime

    class Config:
        from_attributes = True


class MessageWithBot(Message):
    bot: Optional[BotInfo] = None


class MessageInDB(Message):
    user_id: str
