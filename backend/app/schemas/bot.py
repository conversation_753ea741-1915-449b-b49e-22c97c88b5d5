from typing import Optional
from pydantic import BaseModel
from datetime import datetime
from enum import Enum


class BotChannel(str, Enum):
    whatsapp = "whatsapp"
    sms = "sms"
    messenger = "messenger"


class BotStatus(str, Enum):
    active = "active"
    paused = "paused"
    archived = "archived"


class TrainingStatus(str, Enum):
    pending = "pending"
    processing = "processing"
    complete = "complete"
    failed = "failed"


class BotBase(BaseModel):
    name: str
    description: Optional[str] = None
    channel: BotChannel
    status: BotStatus = BotStatus.active
    tone: Optional[str] = None
    language: Optional[str] = None


class BotCreate(BotBase):
    pass


class BotUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    channel: Optional[BotChannel] = None
    status: Optional[BotStatus] = None
    tone: Optional[str] = None
    language: Optional[str] = None


class Bot(BaseModel):
    id: str
    user_id: str
    name: str
    description: Optional[str] = None
    channel: BotChannel
    status: BotStatus
    tone: Optional[str] = None
    language: Optional[str] = None
    training_status: Optional[TrainingStatus] = None
    training_updated_at: Optional[datetime] = None
    embedding_context_id: Optional[str] = None
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class BotInDB(Bot):
    pass
