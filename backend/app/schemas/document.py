from typing import Optional
from pydantic import BaseModel
from datetime import datetime
from enum import Enum


class DocumentType(str, Enum):
    pdf = "pdf"
    txt = "txt"
    docx = "docx"
    chat = "chat"


class EmbeddingStatus(str, Enum):
    pending = "pending"
    processed = "processed"
    failed = "failed"


class DocumentBase(BaseModel):
    file_name: str
    type: DocumentType
    bot_id: Optional[str] = None


class DocumentCreate(DocumentBase):
    file_url: Optional[str] = None


class DocumentUpdate(BaseModel):
    file_name: Optional[str] = None
    embedding_status: Optional[EmbeddingStatus] = None


class Document(BaseModel):
    id: str
    user_id: str
    bot_id: Optional[str] = None
    file_name: str
    file_url: Optional[str] = None
    type: DocumentType
    embedding_status: EmbeddingStatus
    created_at: datetime

    class Config:
        from_attributes = True


class DocumentInDB(Document):
    pass
