from typing import Optional
from pydantic import BaseModel, EmailStr
from datetime import datetime
from enum import Enum


class PlanType(str, Enum):
    free = "free"
    pro = "pro"
    business = "business"


class UserBase(BaseModel):
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    full_name: Optional[str] = None
    email: Optional[EmailStr] = None
    phone_number: Optional[str] = None
    plan: PlanType = PlanType.free
    tokens_used: int = 0
    is_active: Optional[bool] = True
    is_superuser: bool = False


class UserCreate(BaseModel):
    email: EmailStr
    password: str
    first_name: str
    last_name: str


class UserUpdate(BaseModel):
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    phone_number: Optional[str] = None
    plan: Optional[PlanType] = None


class UserProfile(BaseModel):
    id: str
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    full_name: Optional[str] = None
    email: Optional[str] = None
    phone_number: Optional[str] = None
    plan: PlanType
    tokens_used: int
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class User(UserProfile):
    is_active: bool
    is_superuser: bool


class UserInDB(User):
    hashed_password: Optional[str] = None
