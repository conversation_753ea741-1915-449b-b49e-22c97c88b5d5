# Biz Bot Launchpad Backend

FastAPI backend for the Biz Bot Launchpad application.

## Features

- **FastAPI** - Modern, fast web framework for building APIs
- **SQLAlchemy** - SQL toolkit and Object-Relational Mapping (ORM)
- **Alembic** - Database migration tool
- **Pydantic** - Data validation using Python type annotations
- **JWT Authentication** - Secure token-based authentication
- **PostgreSQL** - Production-ready database
- **CORS** - Cross-Origin Resource Sharing support

## Project Structure

```
backend/
├── app/
│   ├── api/
│   │   ├── v1/
│   │   │   ├── endpoints/
│   │   │   │   ├── auth.py      # Authentication endpoints
│   │   │   │   ├── users.py     # User management endpoints
│   │   │   │   ├── bots.py      # Bot management endpoints
│   │   │   │   ├── messages.py  # Message handling endpoints
│   │   │   │   ├── documents.py # Document upload/management
│   │   │   │   └── analytics.py # Analytics and reporting
│   │   │   └── api.py           # API router
│   │   └── deps.py              # Dependencies
│   ├── core/
│   │   ├── config.py            # Configuration settings
│   │   └── security.py          # Security utilities
│   ├── crud/                    # Database operations (placeholder)
│   ├── db/
│   │   ├── base.py              # Database base
│   │   ├── base_class.py        # SQLAlchemy base class
│   │   └── session.py           # Database session
│   ├── models/
│   │   ├── user.py              # User model
│   │   ├── bot.py               # Bot model
│   │   ├── message.py           # Message model
│   │   └── document.py          # Document model
│   ├── schemas/
│   │   ├── auth.py              # Authentication schemas
│   │   ├── user.py              # User schemas
│   │   ├── bot.py               # Bot schemas
│   │   ├── message.py           # Message schemas
│   │   └── document.py          # Document schemas
│   └── main.py                  # FastAPI application
├── alembic/                     # Database migrations
├── requirements.txt             # Python dependencies
├── .env.example                 # Environment variables example
├── start.py                     # Startup script
└── README.md                    # This file
```

## Setup

1. **Create a virtual environment:**
   ```bash
   cd backend
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

2. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

3. **Set up environment variables:**
   ```bash
   cp .env.example .env
   # Edit .env with your actual values (database URL, secret key, etc.)
   ```

4. **Set up the database (Optional - for full database functionality):**
   ```bash
   # Make sure PostgreSQL is running
   # Create the database
   createdb biz_bot_db

   # Run migrations
   alembic upgrade head
   ```

5. **Run the development server:**
   ```bash
   # Option 1: Using the startup script
   python start.py

   # Option 2: Using uvicorn directly
   uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
   ```

## Quick Start (Demo Mode)

The backend includes demo data and mock responses, so you can start immediately without setting up a database:

1. Install dependencies: `pip install -r requirements.txt`
2. Run the server: `python start.py`
3. Open http://localhost:8000/docs to see the API documentation
4. Use demo credentials: `<EMAIL>` / `demo123`

## API Documentation

Once the server is running, you can access:

- **Interactive API docs (Swagger UI):** http://localhost:8000/docs
- **Alternative API docs (ReDoc):** http://localhost:8000/redoc
- **OpenAPI schema:** http://localhost:8000/api/v1/openapi.json

## Environment Variables

Copy `.env.example` to `.env` and configure:

- `DATABASE_URL` - PostgreSQL connection string
- `SECRET_KEY` - JWT secret key (generate a secure one for production)
- `ALLOWED_ORIGINS` - CORS allowed origins (your frontend URL)

## Development

### Database Migrations

Create a new migration:
```bash
alembic revision --autogenerate -m "Description of changes"
```

Apply migrations:
```bash
alembic upgrade head
```

### Testing

```bash
pytest
```

## Production Deployment

1. Set `ENVIRONMENT=production` in your `.env`
2. Use a production WSGI server like Gunicorn:
   ```bash
   pip install gunicorn
   gunicorn app.main:app -w 4 -k uvicorn.workers.UvicornWorker
   ```
3. Set up a reverse proxy (nginx) and SSL certificate
4. Use a production database with proper backup strategy
